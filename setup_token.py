#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إعداد توكن البوت
Bot Token Setup Tool

أداة مساعدة لإعداد توكن البوت ومعرفات المشرفين
"""

import os
import re

def validate_token(token):
    """التحقق من صحة التوكن"""
    # نمط التوكن: رقم:حروف وأرقام
    pattern = r'^\d+:[A-Za-z0-9_-]+$'
    return re.match(pattern, token) is not None

def validate_admin_id(admin_id):
    """التحقق من صحة معرف المشرف"""
    try:
        return int(admin_id) > 0
    except ValueError:
        return False

def setup_token():
    """إعداد توكن البوت"""
    print("🎓 أداة إعداد البوت التعليمي")
    print("=" * 40)
    
    # قراءة الملف الحالي
    try:
        with open('educational_bot.py', 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ ملف educational_bot.py غير موجود!")
        return False
    
    # طلب التوكن
    print("\n🔑 إعداد توكن البوت:")
    print("1. اذهب إلى @BotFather في تيليجرام")
    print("2. أنشئ بوت جديد باستخدام /newbot")
    print("3. انسخ التوكن الذي ستحصل عليه")
    print()
    
    while True:
        token = input("📝 أدخل توكن البوت: ").strip()
        
        if not token:
            print("❌ يرجى إدخال التوكن")
            continue
            
        if not validate_token(token):
            print("❌ تنسيق التوكن غير صحيح")
            print("💡 التوكن يجب أن يكون بالشكل: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz")
            continue
            
        break
    
    # طلب معرفات المشرفين
    print("\n👥 إعداد معرفات المشرفين:")
    print("💡 لمعرفة معرفك، أرسل /start لبوت @userinfobot")
    print("💡 يمكنك إضافة عدة معرفات مفصولة بفاصلة")
    print("💡 اتركه فارغاً إذا كنت لا تريد مشرفين الآن")
    
    admin_ids = []
    admin_input = input("📝 أدخل معرفات المشرفين (اختياري): ").strip()
    
    if admin_input:
        for admin_id in admin_input.split(','):
            admin_id = admin_id.strip()
            if validate_admin_id(admin_id):
                admin_ids.append(int(admin_id))
            else:
                print(f"⚠️ معرف غير صحيح: {admin_id}")
    
    # تحديث الملف
    print("\n🔄 تحديث الملف...")
    
    # استبدال التوكن
    content = content.replace(
        'BOT_TOKEN = "YOUR_BOT_TOKEN_HERE"',
        f'BOT_TOKEN = "{token}"'
    )
    
    # استبدال معرفات المشرفين
    if admin_ids:
        admin_ids_str = ', '.join(map(str, admin_ids))
        content = content.replace(
            'ADMIN_IDS = [123456789]',
            f'ADMIN_IDS = [{admin_ids_str}]'
        )
    
    # حفظ الملف
    try:
        with open('educational_bot.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث الإعدادات بنجاح!")
        print(f"🔑 التوكن: {token[:10]}...")
        if admin_ids:
            print(f"👥 المشرفين: {admin_ids}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في حفظ الملف: {e}")
        return False

def create_env_file():
    """إنشاء ملف .env"""
    print("\n📄 إنشاء ملف .env...")
    
    token = input("🔑 توكن البوت: ").strip()
    admin_ids = input("👥 معرفات المشرفين (مفصولة بفاصلة): ").strip()
    
    env_content = f"""# إعدادات البوت التعليمي
BOT_TOKEN={token}
ADMIN_IDS={admin_ids}

# إعدادات إضافية
LOG_LEVEL=INFO
LOG_FILE=bot.log
"""
    
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ تم إنشاء ملف .env بنجاح!")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف .env: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("اختر طريقة الإعداد:")
    print("1. تحديث ملف educational_bot.py مباشرة")
    print("2. إنشاء ملف .env")
    print("3. الخروج")
    
    while True:
        choice = input("\nاختيارك (1-3): ").strip()
        
        if choice == '1':
            if setup_token():
                print("\n🎉 تم الإعداد بنجاح!")
                print("🚀 يمكنك الآن تشغيل البوت باستخدام:")
                print("   python educational_bot.py")
                print("   أو")
                print("   python run.py")
            break
            
        elif choice == '2':
            if create_env_file():
                print("\n🎉 تم إنشاء ملف .env بنجاح!")
                print("🚀 يمكنك الآن تشغيل البوت باستخدام:")
                print("   python run.py")
            break
            
        elif choice == '3':
            print("👋 وداعاً!")
            break
            
        else:
            print("❌ اختيار غير صحيح، يرجى اختيار 1 أو 2 أو 3")

if __name__ == "__main__":
    main()
