#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إعداد Firebase للبوت التعليمي
Firebase Setup Tool for Educational Bot

أداة مساعدة لإعداد Firebase وإضافة المشرفين الأوائل
"""

import os
import sys
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

def setup_firebase_admins():
    """إعداد المشرفين الأوائل في Firebase"""
    try:
        from firebase_manager import firebase_manager
        
        print("🔧 إعداد المشرفين في Firebase")
        print("=" * 40)
        
        # إضافة مشرف رئيسي
        print("\n👤 إضافة المشرف الرئيسي:")
        admin_id = input("📝 أدخل معرف التليجرام للمشرف الرئيسي: ").strip()
        admin_name = input("📝 أدخل اسم المشرف: ").strip()
        admin_username = input("📝 أدخل اسم المستخدم (اختياري): ").strip()
        
        if not admin_id.isdigit():
            print("❌ معرف التليجرام يجب أن يكون رقماً")
            return False
        
        admin_id = int(admin_id)
        
        # إضافة المشرف إلى Firebase
        success = firebase_manager.add_admin(admin_id, admin_name, admin_username or None)
        
        if success:
            print(f"✅ تم إضافة المشرف: {admin_name} ({admin_id})")
            
            # إضافة مشرفين إضافيين
            while True:
                add_more = input("\n❓ هل تريد إضافة مشرف آخر؟ (y/n): ").strip().lower()
                if add_more not in ['y', 'yes', 'نعم']:
                    break
                
                admin_id = input("📝 معرف التليجرام: ").strip()
                admin_name = input("📝 اسم المشرف: ").strip()
                admin_username = input("📝 اسم المستخدم (اختياري): ").strip()
                
                if admin_id.isdigit():
                    firebase_manager.add_admin(int(admin_id), admin_name, admin_username or None)
                    print(f"✅ تم إضافة المشرف: {admin_name}")
                else:
                    print("❌ معرف غير صحيح، تم تخطيه")
            
            return True
        else:
            print("❌ فشل في إضافة المشرف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إعداد Firebase: {e}")
        return False

def setup_initial_data():
    """إعداد البيانات الأولية في Firebase"""
    try:
        from firebase_manager import firebase_manager
        from educational_bot_firebase import get_default_educational_content, get_default_ministerial_questions, get_default_services_data
        
        print("\n📚 إعداد البيانات الأولية...")
        
        # حفظ المحتوى التعليمي
        educational_content = get_default_educational_content()
        firebase_manager.save_educational_content(educational_content)
        print("✅ تم حفظ المحتوى التعليمي")
        
        # حفظ الأسئلة الوزارية
        ministerial_questions = get_default_ministerial_questions()
        firebase_manager.save_ministerial_questions(ministerial_questions)
        print("✅ تم حفظ الأسئلة الوزارية")
        
        # حفظ بيانات الخدمات
        services_data = get_default_services_data()
        firebase_manager.save_services_data(services_data)
        print("✅ تم حفظ بيانات الخدمات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد البيانات: {e}")
        return False

def test_firebase_connection():
    """اختبار الاتصال بـ Firebase"""
    try:
        from firebase_manager import firebase_manager
        
        print("🔍 اختبار الاتصال بـ Firebase...")
        
        # اختبار Firestore
        stats = firebase_manager.get_bot_statistics()
        print("✅ الاتصال بـ Firestore يعمل")
        
        # اختبار Realtime Database
        firebase_manager.log_bot_activity("test", {"message": "اختبار الاتصال"})
        print("✅ الاتصال بـ Realtime Database يعمل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ Firebase: {e}")
        return False

def check_environment():
    """التحقق من متغيرات البيئة"""
    print("🔍 التحقق من متغيرات البيئة...")
    
    required_vars = [
        'BOT_TOKEN',
        'FIREBASE_PROJECT_ID',
        'FIREBASE_PRIVATE_KEY',
        'FIREBASE_CLIENT_EMAIL',
        'FIREBASE_DATABASE_URL'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ متغيرات البيئة المفقودة:")
        for var in missing_vars:
            print(f"   • {var}")
        print("\n💡 تأكد من وجود ملف .env مع جميع المتغيرات المطلوبة")
        return False
    else:
        print("✅ جميع متغيرات البيئة موجودة")
        return True

def create_env_file():
    """إنشاء ملف .env من القالب"""
    if os.path.exists('.env'):
        overwrite = input("⚠️ ملف .env موجود بالفعل. هل تريد استبداله؟ (y/n): ").strip().lower()
        if overwrite not in ['y', 'yes', 'نعم']:
            return False
    
    try:
        # نسخ من .env.example
        if os.path.exists('.env.example'):
            import shutil
            shutil.copy('.env.example', '.env')
            print("✅ تم إنشاء ملف .env من القالب")
            print("📝 يرجى تحديث القيم في ملف .env")
            return True
        else:
            print("❌ ملف .env.example غير موجود")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف .env: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة إعداد Firebase للبوت التعليمي")
    print("=" * 50)
    
    # التحقق من ملف .env
    if not os.path.exists('.env'):
        print("⚠️ ملف .env غير موجود")
        if input("❓ هل تريد إنشاؤه من القالب؟ (y/n): ").strip().lower() in ['y', 'yes', 'نعم']:
            if not create_env_file():
                print("❌ فشل في إنشاء ملف .env")
                return
            print("📝 يرجى تحديث ملف .env بالقيم الصحيحة ثم تشغيل الأداة مرة أخرى")
            return
        else:
            print("❌ لا يمكن المتابعة بدون ملف .env")
            return
    
    # التحقق من متغيرات البيئة
    if not check_environment():
        return
    
    # اختبار الاتصال
    if not test_firebase_connection():
        print("❌ فشل في الاتصال بـ Firebase")
        print("💡 تحقق من إعدادات Firebase في ملف .env")
        return
    
    print("\n🎯 اختر العملية المطلوبة:")
    print("1. إعداد المشرفين")
    print("2. إعداد البيانات الأولية")
    print("3. إعداد شامل (المشرفين + البيانات)")
    print("4. اختبار الاتصال فقط")
    print("5. الخروج")
    
    while True:
        choice = input("\nاختيارك (1-5): ").strip()
        
        if choice == '1':
            if setup_firebase_admins():
                print("\n🎉 تم إعداد المشرفين بنجاح!")
            break
            
        elif choice == '2':
            if setup_initial_data():
                print("\n🎉 تم إعداد البيانات الأولية بنجاح!")
            break
            
        elif choice == '3':
            print("\n📋 إعداد شامل...")
            admins_ok = setup_firebase_admins()
            data_ok = setup_initial_data()
            
            if admins_ok and data_ok:
                print("\n🎉 تم الإعداد الشامل بنجاح!")
                print("🚀 يمكنك الآن تشغيل البوت باستخدام:")
                print("   python educational_bot_firebase.py")
            break
            
        elif choice == '4':
            test_firebase_connection()
            break
            
        elif choice == '5':
            print("👋 وداعاً!")
            break
            
        else:
            print("❌ اختيار غير صحيح، يرجى اختيار رقم من 1 إلى 5")

if __name__ == "__main__":
    main()
