#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إعدادات البوت التعليمي
Educational Bot Configuration

يحتوي على جميع الإعدادات والبيانات الثابتة للبوت
"""

# إعدادات البوت الأساسية
BOT_CONFIG = {
    "name": "البوت التعليمي المتطور",
    "version": "1.0.0",
    "developer": "Augment Agent",
    "description": "بوت تيليجرام تعليمي متعدد الأقسام",
    "support_username": "@support_team"
}

# رسائل النظام
SYSTEM_MESSAGES = {
    "welcome": """
🎓 **أهلاً وسهلاً بك في البوت التعليمي المتطور!**

مرحباً بك في منصتك التعليمية الشاملة التي تضم:

📝 **الترجمة** - ترجمة الملفات والنصوص من الإنجليزية للعربية
📖 **المحتوى العلمي** - مواد دراسية منظمة حسب المرحلة
🗂️ **المواد الوزارية** - أسئلة وزارية مع الحلول واختبارات تفاعلية
🛠️ **الخدمات** - خدمات متنوعة من فريق المنصة
💡 **اقتراح فكرة جديدة** - شاركنا أفكارك الإبداعية

اختر القسم الذي تريده من الأزرار أدناه ⬇️
""",
    
    "help": """
🆘 **دليل استخدام البوت**

**الأوامر المتاحة:**
/start - بدء استخدام البوت
/help - عرض هذه المساعدة
/status - حالة البوت

**الأقسام المتاحة:**
📝 الترجمة - ترجمة فورية للنصوص والملفات
📖 المحتوى العلمي - مواد دراسية شاملة
🗂️ المواد الوزارية - أسئلة وزارية واختبارات
🛠️ الخدمات - خدمات احترافية متنوعة
💡 اقتراح الأفكار - شاركنا اقتراحاتك

**للدعم الفني:** @support_team
""",
    
    "unknown_command": """
🤔 **لم أفهم طلبك**

يرجى استخدام الأزرار الموجودة أدناه للتنقل في البوت.

💡 **نصائح:**
• استخدم الأزرار بدلاً من كتابة النص
• للمساعدة اكتب /help
• للبدء من جديد اكتب /start
""",
    
    "error": "❌ حدث خطأ مؤقت. يرجى المحاولة مرة أخرى.",
    "processing": "🔄 جاري المعالجة...",
    "success": "✅ تمت العملية بنجاح!",
    "back_to_main": "🏠 تم العودة للقائمة الرئيسية"
}

# إعدادات الملفات
FILE_CONFIG = {
    "max_size": 20 * 1024 * 1024,  # 20 MB
    "allowed_types": [".pdf", ".doc", ".docx", ".txt"],
    "upload_path": "uploads/",
    "translated_path": "translated/"
}

# إعدادات الاختبارات
TEST_CONFIG = {
    "passing_score": 60,  # النسبة المئوية للنجاح
    "excellent_score": 90,
    "very_good_score": 80,
    "good_score": 70,
    "questions_per_test": 10,
    "time_limit": 30  # بالدقائق
}

# تقييمات الاختبارات
TEST_GRADES = {
    90: {"grade": "ممتاز 🌟", "emoji": "🎉", "message": "أداء رائع! استمر في التميز"},
    80: {"grade": "جيد جداً 👍", "emoji": "👏", "message": "أداء ممتاز! تحسن ملحوظ"},
    70: {"grade": "جيد 👌", "emoji": "😊", "message": "أداء جيد! يمكنك تحسين أكثر"},
    60: {"grade": "مقبول 📚", "emoji": "🤔", "message": "أداء مقبول، راجع المادة مرة أخرى"},
    0: {"grade": "يحتاج تحسين 📖", "emoji": "💪", "message": "لا تيأس! المزيد من الدراسة سيحسن نتائجك"}
}

# أفكار إضافية من المستخدمين (كما طلبت)
ADDITIONAL_IDEAS = {
    "رقية هيثم عبد مهدي": {
        "stage": "4",
        "ideas": [
            "دعم إجابة الأسئلة الوزارية الشائعة وغير التقليدية",
            "إضافة صور تشخيصية غير معقدة لتدريب الطلاب على تمييز الصور الإشعاعية",
            "تدريب على الحالات المرضية البسيطة"
        ]
    },
    "نبأ ميثم هاشم حسن": {
        "stage": "3",
        "ideas": [
            "شرح مكونات الأجهزة الطبية",
            "توضيح وظيفة كل مكون في الأجهزة",
            "طريقة تشغيل الأجهزة الطبية",
            "الأمراض المرتبطة بكل جهاز طبي"
        ]
    }
}

# إعدادات قاعدة البيانات (للتطوير المستقبلي)
DATABASE_CONFIG = {
    "type": "sqlite",
    "name": "educational_bot.db",
    "tables": {
        "users": ["id", "username", "first_name", "last_name", "join_date", "last_activity"],
        "ideas": ["id", "user_id", "name", "author", "description", "category", "date", "status"],
        "test_results": ["id", "user_id", "subject", "year", "score", "total", "date"],
        "user_progress": ["id", "user_id", "stage", "subject", "progress", "last_update"]
    }
}

# إعدادات التسجيل
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "bot.log",
    "max_size": 10 * 1024 * 1024,  # 10 MB
    "backup_count": 5
}

# إعدادات الإشعارات
NOTIFICATION_CONFIG = {
    "admin_notifications": True,
    "user_notifications": True,
    "error_notifications": True,
    "startup_notification": True,
    "shutdown_notification": True
}

# رسائل الإشعارات الاحترافية
CALLBACK_MESSAGES = {
    "section_selected": "تم اختيار هذا القسم ✅",
    "processing": "جاري المعالجة... ⏳",
    "correct_answer": "✅ إجابة صحيحة!",
    "wrong_answer": "❌ إجابة خاطئة",
    "test_completed": "🎯 تم إنهاء الاختبار",
    "file_uploaded": "📎 تم رفع الملف بنجاح",
    "translation_started": "🔄 بدء الترجمة...",
    "idea_submitted": "💡 تم إرسال الفكرة للمراجعة"
}

# إعدادات الأمان
SECURITY_CONFIG = {
    "rate_limit": 30,  # عدد الرسائل في الدقيقة
    "max_file_size": 20 * 1024 * 1024,  # 20 MB
    "allowed_file_types": [".pdf", ".doc", ".docx", ".txt"],
    "blocked_words": [],  # كلمات محظورة
    "admin_only_commands": ["/admin", "/broadcast", "/stats"]
}

# إعدادات التطوير المستقبلي
FUTURE_FEATURES = {
    "video_courses": {
        "status": "قيد التطوير",
        "description": "كورسات فيديو تعليمية مع اختبارات",
        "expected_date": "قريباً"
    },
    "ai_translation": {
        "status": "مخطط",
        "description": "ترجمة بالذكاء الاصطناعي",
        "expected_date": "الإصدار القادم"
    },
    "mobile_app": {
        "status": "مخطط",
        "description": "تطبيق جوال مصاحب",
        "expected_date": "2025"
    }
}
