# 🚀 دليل التشغيل السريع

## خطوات التشغيل السريع

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. إعداد توكن البوت
1. اذه<PERSON> إلى [@BotFather](https://t.me/BotFather) في تيليجرام
2. أنشئ بوت جديد باستخدام `/newbot`
3. احصل على التوكن
4. افتح ملف `educational_bot.py`
5. استبدل `YOUR_BOT_TOKEN_HERE` بالتوكن الخاص بك

```python
BOT_TOKEN = "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz"  # ضع توكنك هنا
```

### 3. إعداد المشرفين (اختياري)
```python
ADMIN_IDS = [123456789, 987654321]  # ضع معرفات المشرفين
```

### 4. تشغيل البوت
```bash
python educational_bot.py
```

أو استخدم الملف المحسن:
```bash
python run.py
```

### 5. اختبار البوت
```bash
python test_bot.py
```

## ✅ التحقق من التشغيل

إذا ظهرت هذه الرسائل، فالبوت يعمل بنجاح:
```
🚀 بدء تشغيل البوت التعليمي...
📊 تم تحميل 4 مراحل دراسية
🗂️ تم تحميل 1 مواد وزارية
🛠️ تم تحميل 4 خدمات
✅ البوت جاهز لاستقبال الرسائل
```

## 🎯 الاستخدام الأساسي

1. ابحث عن البوت في تيليجرام
2. اضغط `/start`
3. اختر القسم المطلوب من الكيبورد
4. اتبع التعليمات التفاعلية

## 🔧 استكشاف الأخطاء

### خطأ: "No module named 'telebot'"
```bash
pip install pyTelegramBotAPI
```

### خطأ: "Invalid token"
- تأكد من صحة التوكن
- تأكد من عدم وجود مسافات إضافية

### خطأ: "Connection error"
- تحقق من الاتصال بالإنترنت
- تأكد من عدم حظر تيليجرام

## 📞 الدعم

للمساعدة أو الاستفسارات:
- راجع ملف `README.md` للتفاصيل الكاملة
- شغل `python test_bot.py` للتحقق من المشاكل
