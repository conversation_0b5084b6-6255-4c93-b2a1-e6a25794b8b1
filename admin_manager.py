#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إدارة المشرفين للبوت التعليمي
تسمح بإضافة وإزالة المشرفين من Firebase
"""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from firebase_manager import FirebaseManager

class AdminManager:
    def __init__(self):
        """تهيئة مدير المشرفين"""
        self.firebase_manager = FirebaseManager()
        print("🔧 تم تهيئة مدير المشرفين")
    
    def add_admin(self, telegram_id: int, name: str = None, role: str = "مشرف"):
        """إضافة مشرف جديد"""
        try:
            # التحقق من وجود المشرف مسبقاً
            existing_admins = self.firebase_manager.get_admin_ids()
            if telegram_id in existing_admins:
                print(f"⚠️ المشرف {telegram_id} موجود مسبقاً")
                return False
            
            # بيانات المشرف الأساسية
            admin_data = {
                'telegram_id': telegram_id,
                'name': name or f"مشرف_{telegram_id}",
                'role': role,
                'active': True,
                'created_date': datetime.now(),
                'permissions': {
                    'manage_users': True,
                    'manage_content': True,
                    'view_statistics': True,
                    'manage_ideas': True
                }
            }
            
            # حفظ في Firebase
            success = self.firebase_manager.add_admin(telegram_id, admin_data)
            
            if success:
                print(f"✅ تم إضافة المشرف {telegram_id} بنجاح")
                print(f"📝 الاسم: {admin_data['name']}")
                print(f"🎭 الدور: {admin_data['role']}")
                return True
            else:
                print(f"❌ فشل في إضافة المشرف {telegram_id}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في إضافة المشرف: {e}")
            return False
    
    def remove_admin(self, telegram_id: int):
        """إزالة مشرف"""
        try:
            success = self.firebase_manager.remove_admin(telegram_id)
            
            if success:
                print(f"✅ تم إزالة المشرف {telegram_id} بنجاح")
                return True
            else:
                print(f"❌ فشل في إزالة المشرف {telegram_id}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في إزالة المشرف: {e}")
            return False
    
    def list_admins(self):
        """عرض قائمة المشرفين"""
        try:
            admin_ids = self.firebase_manager.get_admin_ids()
            
            if not admin_ids:
                print("📋 لا يوجد مشرفين مسجلين")
                return
            
            print(f"📋 قائمة المشرفين ({len(admin_ids)} مشرف):")
            print("=" * 50)
            
            for admin_id in admin_ids:
                # الحصول على تفاصيل المشرف
                admin_data = self.firebase_manager.get_admin_details(admin_id)
                if admin_data:
                    print(f"🆔 ID: {admin_id}")
                    print(f"📝 الاسم: {admin_data.get('name', 'غير محدد')}")
                    print(f"🎭 الدور: {admin_data.get('role', 'مشرف')}")
                    print(f"✅ نشط: {'نعم' if admin_data.get('active', False) else 'لا'}")
                    print("-" * 30)
                else:
                    print(f"🆔 ID: {admin_id} (بيانات غير مكتملة)")
                    print("-" * 30)
                    
        except Exception as e:
            print(f"❌ خطأ في عرض المشرفين: {e}")
    
    def update_admin_info(self, telegram_id: int, user_info: dict):
        """تحديث معلومات المشرف عند دخوله للبوت"""
        try:
            update_data = {
                'last_login': datetime.now(),
                'username': user_info.get('username'),
                'first_name': user_info.get('first_name'),
                'last_name': user_info.get('last_name'),
                'language_code': user_info.get('language_code', 'ar')
            }
            
            # تحديث الاسم إذا لم يكن محدد مسبقاً
            if user_info.get('first_name'):
                full_name = user_info.get('first_name', '')
                if user_info.get('last_name'):
                    full_name += f" {user_info.get('last_name')}"
                update_data['name'] = full_name
            
            success = self.firebase_manager.update_admin_info(telegram_id, update_data)
            
            if success:
                print(f"✅ تم تحديث معلومات المشرف {telegram_id}")
                return True
            else:
                print(f"❌ فشل في تحديث معلومات المشرف {telegram_id}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تحديث معلومات المشرف: {e}")
            return False

def main():
    """الدالة الرئيسية للتفاعل مع المستخدم"""
    admin_manager = AdminManager()
    
    print("\n🤖 مرحباً بك في أداة إدارة المشرفين")
    print("=" * 50)
    
    while True:
        print("\n📋 الخيارات المتاحة:")
        print("1️⃣ إضافة مشرف جديد")
        print("2️⃣ إزالة مشرف")
        print("3️⃣ عرض قائمة المشرفين")
        print("4️⃣ الخروج")
        
        choice = input("\n🔢 اختر رقم العملية: ").strip()
        
        if choice == "1":
            print("\n➕ إضافة مشرف جديد")
            try:
                telegram_id = int(input("🆔 أدخل Telegram ID للمشرف: ").strip())
                name = input("📝 أدخل اسم المشرف (اختياري): ").strip()
                role = input("🎭 أدخل دور المشرف (افتراضي: مشرف): ").strip()
                
                if not name:
                    name = None
                if not role:
                    role = "مشرف"
                
                admin_manager.add_admin(telegram_id, name, role)
                
            except ValueError:
                print("❌ يرجى إدخال رقم صحيح للـ Telegram ID")
            except Exception as e:
                print(f"❌ خطأ: {e}")
        
        elif choice == "2":
            print("\n➖ إزالة مشرف")
            try:
                telegram_id = int(input("🆔 أدخل Telegram ID للمشرف المراد إزالته: ").strip())
                confirm = input(f"⚠️ هل أنت متأكد من إزالة المشرف {telegram_id}؟ (نعم/لا): ").strip().lower()
                
                if confirm in ['نعم', 'yes', 'y']:
                    admin_manager.remove_admin(telegram_id)
                else:
                    print("❌ تم إلغاء العملية")
                    
            except ValueError:
                print("❌ يرجى إدخال رقم صحيح للـ Telegram ID")
            except Exception as e:
                print(f"❌ خطأ: {e}")
        
        elif choice == "3":
            print("\n📋 قائمة المشرفين")
            admin_manager.list_admins()
        
        elif choice == "4":
            print("\n👋 شكراً لاستخدام أداة إدارة المشرفين")
            break
        
        else:
            print("❌ خيار غير صحيح، يرجى المحاولة مرة أخرى")

if __name__ == "__main__":
    main()
