# 🎓 بوت تيليجرام تعليمي مع Firebase

بوت تيليجرام احترافي ومتطور يوفر خدمات تعليمية شاملة مع قاعدة بيانات Firebase Realtime Database و Firestore.

## ✨ المميزات الجديدة مع Firebase

### 🔥 Firebase Integration
- **Firestore Database** - لحفظ البيانات المنظمة (المستخدمين، الأفكار، نتائج الاختبارات)
- **Realtime Database** - لتتبع الأنشطة والإحصائيات الفورية
- **إدارة المشرفين** - نظام ديناميكي لإدارة المشرفين من قاعدة البيانات
- **نسخ احتياطي تلقائي** - حفظ البيانات بشكل آمن ومستمر

### 📊 إدارة البيانات المتقدمة
- حفظ جميع بيانات المستخدمين في Firestore
- تتبع أنشطة المستخدمين في الوقت الفعلي
- إحصائيات شاملة ومفصلة
- نظام إدارة الأفكار المقترحة المحسن

### 🔐 الأمان والخصوصية
- جميع الإعدادات الحساسة في متغيرات البيئة
- لا توجد معلومات حساسة في الكود
- نظام صلاحيات متقدم للمشرفين

## 🚀 التثبيت والإعداد

### المتطلبات
- Python 3.8 أو أحدث
- حساب Firebase مع مشروع جديد
- توكن بوت تيليجرام

### خطوات الإعداد

#### 1. إعداد Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. أنشئ مشروع جديد
3. فعّل Firestore Database
4. فعّل Realtime Database
5. أنشئ Service Account وحمّل ملف JSON

#### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 3. إعداد متغيرات البيئة
```bash
# انسخ ملف القالب
cp .env.example .env

# حرر الملف وأضف بياناتك
nano .env
```

#### 4. إعداد Firebase والمشرفين
```bash
python setup_firebase.py
```

#### 5. تشغيل البوت
```bash
python educational_bot_firebase.py
```

## ⚙️ إعداد متغيرات البيئة

أنشئ ملف `.env` مع المتغيرات التالية:

```env
# إعدادات البوت
BOT_TOKEN=your_telegram_bot_token

# إعدادات Firebase
FIREBASE_TYPE=service_account
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40your-project.iam.gserviceaccount.com
FIREBASE_UNIVERSE_DOMAIN=googleapis.com
FIREBASE_DATABASE_URL=https://your-project-default-rtdb.firebaseio.com

# إعدادات إضافية
LOG_LEVEL=INFO
LOG_FILE=bot.log
```

## 🗄️ هيكل قاعدة البيانات

### Firestore Collections

#### `admins` - المشرفين
```json
{
  "telegram_id": *********,
  "name": "اسم المشرف",
  "username": "username",
  "added_date": "2025-07-12T10:00:00Z",
  "active": true,
  "permissions": {
    "manage_users": true,
    "manage_content": true,
    "view_stats": true
  }
}
```

#### `users` - المستخدمين
```json
{
  "telegram_id": *********,
  "username": "username",
  "first_name": "الاسم الأول",
  "last_name": "الاسم الأخير",
  "join_date": "2025-07-12T10:00:00Z",
  "last_activity": "2025-07-12T10:00:00Z",
  "stats": {
    "total_tests": 5,
    "average_score": 85.5,
    "best_score": 95
  }
}
```

#### `suggested_ideas` - الأفكار المقترحة
```json
{
  "name": "اسم الفكرة",
  "author": "صاحب الفكرة",
  "description": "وصف الفكرة",
  "category": "القسم",
  "user_id": *********,
  "created_date": "2025-07-12T10:00:00Z",
  "status": "جديدة"
}
```

#### `test_results` - نتائج الاختبارات
```json
{
  "user_id": *********,
  "subject": "الفيزياء الطبية",
  "year": "2023",
  "score": 8,
  "total": 10,
  "date": "2025-07-12T10:00:00Z",
  "answers": [...]
}
```

### Realtime Database Structure

```
/
├── user_activities/
│   └── {user_id}/
│       └── {activity_id}: {
│           "action": "started_test",
│           "timestamp": "2025-07-12T10:00:00Z"
│         }
└── bot_activities/
    └── {activity_id}: {
        "type": "user_joined",
        "details": {...},
        "timestamp": "2025-07-12T10:00:00Z"
      }
```

## 🔧 إدارة المشرفين

### إضافة مشرف جديد
```python
from firebase_manager import firebase_manager

firebase_manager.add_admin(
    telegram_id=*********,
    name="اسم المشرف",
    username="username"
)
```

### إزالة مشرف
```python
firebase_manager.remove_admin(*********)
```

### عرض جميع المشرفين
```python
admins = firebase_manager.get_admin_ids()
print(f"المشرفين النشطين: {admins}")
```

## 📊 الإحصائيات والتقارير

### الحصول على إحصائيات شاملة
```python
stats = firebase_manager.get_bot_statistics()
print(f"عدد المستخدمين: {stats['total_users']}")
print(f"عدد الأفكار: {stats['total_ideas']}")
print(f"عدد الاختبارات: {stats['total_tests']}")
```

### عرض الأفكار المقترحة
```python
ideas = firebase_manager.get_ideas(limit=10)
for idea in ideas:
    print(f"الفكرة: {idea['name']} - المؤلف: {idea['author']}")
```

## 🛠️ الصيانة والنسخ الاحتياطي

### إنشاء نسخة احتياطية
```python
firebase_manager.backup_data()
```

### تنظيف البيانات القديمة
```python
firebase_manager.cleanup_old_data(days=30)
```

## 🔍 استكشاف الأخطاء

### خطأ في الاتصال بـ Firebase
```bash
# تحقق من متغيرات البيئة
python -c "import os; from dotenv import load_dotenv; load_dotenv(); print('Firebase Project:', os.getenv('FIREBASE_PROJECT_ID'))"

# اختبر الاتصال
python setup_firebase.py
```

### خطأ في الصلاحيات
- تأكد من تفعيل Firestore و Realtime Database في Firebase Console
- تحقق من صلاحيات Service Account

### خطأ في البيانات
```python
# إعادة تحميل البيانات الافتراضية
python setup_firebase.py
```

## 📈 التطوير والتخصيص

### إضافة مجموعة جديدة في Firestore
```python
# في firebase_manager.py
def save_custom_data(self, collection_name, data):
    try:
        self.db_client.collection(collection_name).add(data)
        return True
    except Exception as e:
        logger.error(f"خطأ في حفظ البيانات: {e}")
        return False
```

### إضافة إحصائية جديدة
```python
# في get_bot_statistics()
stats['custom_metric'] = self.calculate_custom_metric()
```

## 🚀 النشر والإنتاج

### متطلبات الإنتاج
- خادم مع Python 3.8+
- اتصال مستقر بالإنترنت
- مشروع Firebase مع خطة مدفوعة (للاستخدام المكثف)

### إعداد البيئة الإنتاجية
```bash
# تثبيت المتطلبات
pip install -r requirements.txt

# إعداد متغيرات البيئة
export BOT_TOKEN="your_token"
export FIREBASE_PROJECT_ID="your_project"

# تشغيل البوت
nohup python educational_bot_firebase.py &
```

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

**تم التطوير بواسطة:** Augment Agent  
**التاريخ:** 2025-07-12  
**الإصدار:** 2.0.0 (Firebase Edition)
