#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات البوت التعليمي مع Firebase
Educational Bot with Firebase Tests

ملف اختبار شامل للتحقق من عمل البوت مع Firebase
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# تحميل متغيرات البيئة للاختبار
os.environ['BOT_TOKEN'] = 'test_token'
os.environ['FIREBASE_PROJECT_ID'] = 'test_project'
os.environ['FIREBASE_PRIVATE_KEY'] = 'test_key'
os.environ['FIREBASE_CLIENT_EMAIL'] = '<EMAIL>'
os.environ['FIREBASE_DATABASE_URL'] = 'https://test.firebaseio.com'

class TestFirebaseManager(unittest.TestCase):
    """فئة اختبار مدير Firebase"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.test_user_id = 12345
        self.test_admin_id = 67890
        
    @patch('firebase_manager.firebase_admin')
    @patch('firebase_manager.firestore')
    @patch('firebase_manager.db')
    def test_firebase_initialization(self, mock_db, mock_firestore, mock_firebase_admin):
        """اختبار تهيئة Firebase"""
        # محاكاة Firebase
        mock_firebase_admin._apps = []
        mock_firestore.client.return_value = MagicMock()
        mock_db.reference.return_value = MagicMock()
        
        try:
            from firebase_manager import FirebaseManager
            manager = FirebaseManager()
            self.assertIsNotNone(manager.db_client)
            self.assertIsNotNone(manager.realtime_db)
        except Exception as e:
            # متوقع في بيئة الاختبار بدون Firebase حقيقي
            self.assertIn("Firebase", str(e))
    
    def test_admin_management(self):
        """اختبار إدارة المشرفين"""
        # محاكاة Firebase Manager
        with patch('firebase_manager.firebase_manager') as mock_manager:
            mock_manager.get_admin_ids.return_value = [self.test_admin_id]
            mock_manager.add_admin.return_value = True
            mock_manager.remove_admin.return_value = True
            
            # اختبار الحصول على المشرفين
            admins = mock_manager.get_admin_ids()
            self.assertIn(self.test_admin_id, admins)
            
            # اختبار إضافة مشرف
            result = mock_manager.add_admin(999, "Test Admin", "test_admin")
            self.assertTrue(result)
            
            # اختبار إزالة مشرف
            result = mock_manager.remove_admin(999)
            self.assertTrue(result)
    
    def test_user_data_management(self):
        """اختبار إدارة بيانات المستخدمين"""
        with patch('firebase_manager.firebase_manager') as mock_manager:
            test_user_data = {
                'telegram_id': self.test_user_id,
                'username': 'test_user',
                'first_name': 'Test',
                'last_name': 'User',
                'join_date': datetime.now()
            }
            
            mock_manager.save_user.return_value = True
            mock_manager.get_user.return_value = test_user_data
            mock_manager.update_user_activity.return_value = True
            
            # اختبار حفظ المستخدم
            result = mock_manager.save_user(self.test_user_id, test_user_data)
            self.assertTrue(result)
            
            # اختبار الحصول على بيانات المستخدم
            user_data = mock_manager.get_user(self.test_user_id)
            self.assertEqual(user_data['telegram_id'], self.test_user_id)
            
            # اختبار تحديث النشاط
            result = mock_manager.update_user_activity(self.test_user_id, "test_action")
            self.assertTrue(result)
    
    def test_ideas_management(self):
        """اختبار إدارة الأفكار المقترحة"""
        with patch('firebase_manager.firebase_manager') as mock_manager:
            test_idea = {
                'name': 'فكرة اختبار',
                'author': 'مؤلف الاختبار',
                'description': 'وصف الفكرة للاختبار',
                'category': 'اختبار',
                'user_id': self.test_user_id
            }
            
            mock_manager.save_idea.return_value = 'test_idea_id'
            mock_manager.get_ideas.return_value = [test_idea]
            mock_manager.update_idea_status.return_value = True
            
            # اختبار حفظ الفكرة
            idea_id = mock_manager.save_idea(test_idea)
            self.assertEqual(idea_id, 'test_idea_id')
            
            # اختبار الحصول على الأفكار
            ideas = mock_manager.get_ideas(5)
            self.assertEqual(len(ideas), 1)
            self.assertEqual(ideas[0]['name'], 'فكرة اختبار')
            
            # اختبار تحديث حالة الفكرة
            result = mock_manager.update_idea_status('test_idea_id', 'مراجعة', self.test_admin_id)
            self.assertTrue(result)
    
    def test_statistics(self):
        """اختبار الإحصائيات"""
        with patch('firebase_manager.firebase_manager') as mock_manager:
            test_stats = {
                'total_users': 100,
                'total_ideas': 25,
                'total_tests': 500,
                'active_admins': 3,
                'last_update': datetime.now()
            }
            
            mock_manager.get_bot_statistics.return_value = test_stats
            
            # اختبار الحصول على الإحصائيات
            stats = mock_manager.get_bot_statistics()
            self.assertEqual(stats['total_users'], 100)
            self.assertEqual(stats['total_ideas'], 25)
            self.assertEqual(stats['total_tests'], 500)

class TestBotWithFirebase(unittest.TestCase):
    """فئة اختبار البوت مع Firebase"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.test_user_id = 12345
        
    @patch('educational_bot_firebase.firebase_manager')
    def test_bot_initialization(self, mock_manager):
        """اختبار تهيئة البوت"""
        mock_manager.get_admin_ids.return_value = [67890]
        mock_manager.get_educational_content.return_value = {}
        mock_manager.get_ministerial_questions.return_value = {}
        mock_manager.get_services_data.return_value = {}
        
        try:
            import educational_bot_firebase
            self.assertIsNotNone(educational_bot_firebase.BOT_TOKEN)
            self.assertIsInstance(educational_bot_firebase.ADMIN_IDS, list)
        except Exception as e:
            # متوقع في بيئة الاختبار
            pass
    
    @patch('educational_bot_firebase.firebase_manager')
    def test_user_data_functions(self, mock_manager):
        """اختبار وظائف بيانات المستخدم"""
        test_data = {'test_key': 'test_value'}
        mock_manager.get_user.return_value = test_data
        mock_manager.save_user.return_value = True
        
        try:
            from educational_bot_firebase import get_user_data, save_user_data
            
            # اختبار الحصول على البيانات
            user_data = get_user_data(self.test_user_id)
            self.assertEqual(user_data['test_key'], 'test_value')
            
            # اختبار حفظ البيانات
            save_user_data(self.test_user_id, test_data)
            mock_manager.save_user.assert_called_with(self.test_user_id, test_data)
            
        except ImportError:
            # متوقع إذا لم يتم تحميل Firebase بشكل صحيح
            pass
    
    def test_default_data_functions(self):
        """اختبار وظائف البيانات الافتراضية"""
        try:
            from educational_bot_firebase import (
                get_default_educational_content,
                get_default_ministerial_questions,
                get_default_services_data
            )
            
            # اختبار المحتوى التعليمي
            educational_content = get_default_educational_content()
            self.assertIsInstance(educational_content, dict)
            self.assertGreater(len(educational_content), 0)
            
            # اختبار الأسئلة الوزارية
            ministerial_questions = get_default_ministerial_questions()
            self.assertIsInstance(ministerial_questions, dict)
            self.assertGreater(len(ministerial_questions), 0)
            
            # اختبار بيانات الخدمات
            services_data = get_default_services_data()
            self.assertIsInstance(services_data, dict)
            self.assertGreater(len(services_data), 0)
            
        except ImportError:
            # متوقع إذا لم يتم تحميل الوحدات بشكل صحيح
            pass

class TestEnvironmentVariables(unittest.TestCase):
    """فئة اختبار متغيرات البيئة"""
    
    def test_required_env_vars(self):
        """اختبار متغيرات البيئة المطلوبة"""
        required_vars = [
            'BOT_TOKEN',
            'FIREBASE_PROJECT_ID',
            'FIREBASE_PRIVATE_KEY',
            'FIREBASE_CLIENT_EMAIL',
            'FIREBASE_DATABASE_URL'
        ]
        
        for var in required_vars:
            value = os.getenv(var)
            self.assertIsNotNone(value, f"متغير البيئة {var} مفقود")
            self.assertNotEqual(value, '', f"متغير البيئة {var} فارغ")
    
    def test_firebase_config_creation(self):
        """اختبار إنشاء إعدادات Firebase"""
        try:
            from firebase_manager import FirebaseManager
            manager = FirebaseManager()
            config = manager._create_firebase_config()
            
            self.assertIn('project_id', config)
            self.assertIn('client_email', config)
            self.assertIn('private_key', config)
            
        except Exception:
            # متوقع في بيئة الاختبار
            pass

def run_firebase_tests():
    """تشغيل اختبارات Firebase"""
    print("🧪 بدء اختبارات Firebase...")
    
    # اختبار متغيرات البيئة
    required_vars = [
        'BOT_TOKEN', 'FIREBASE_PROJECT_ID', 'FIREBASE_PRIVATE_KEY',
        'FIREBASE_CLIENT_EMAIL', 'FIREBASE_DATABASE_URL'
    ]
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print("❌ متغيرات البيئة المفقودة:")
        for var in missing_vars:
            print(f"   • {var}")
        return False
    
    print("✅ جميع متغيرات البيئة موجودة")
    
    # اختبار استيراد الوحدات
    try:
        import educational_bot_firebase
        print("✅ تم استيراد البوت بنجاح")
    except Exception as e:
        print(f"❌ خطأ في استيراد البوت: {e}")
        return False
    
    try:
        from firebase_manager import firebase_manager
        print("✅ تم استيراد مدير Firebase بنجاح")
    except Exception as e:
        print(f"❌ خطأ في استيراد مدير Firebase: {e}")
        return False
    
    return True

def main():
    """الدالة الرئيسية للاختبارات"""
    print("🎓 اختبارات البوت التعليمي مع Firebase")
    print("=" * 50)
    
    # تشغيل الاختبارات الأساسية
    if not run_firebase_tests():
        print("\n❌ فشلت الاختبارات الأساسية")
        return
    
    print("\n🧪 تشغيل الاختبارات المتقدمة...")
    
    # تشغيل اختبارات unittest
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # إضافة اختبارات Firebase
    suite.addTests(loader.loadTestsFromTestCase(TestFirebaseManager))
    suite.addTests(loader.loadTestsFromTestCase(TestBotWithFirebase))
    suite.addTests(loader.loadTestsFromTestCase(TestEnvironmentVariables))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # عرض النتائج
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ البوت مع Firebase جاهز للتشغيل")
        print("\n🚀 للبدء:")
        print("   1. python setup_firebase.py")
        print("   2. python educational_bot_firebase.py")
    else:
        print("❌ بعض الاختبارات فشلت")
        print(f"🔍 الأخطاء: {len(result.errors)}")
        print(f"🔍 الفشل: {len(result.failures)}")

if __name__ == "__main__":
    main()
