# 🔧 أداة إدارة المشرفين - Admin Manager

أداة بسيطة وفعالة لإدارة المشرفين في البوت التعليمي باستخدام Firebase.

## 🚀 المميزات

- ✅ إضافة مشرفين جدد بسهولة
- ✅ إزالة المشرفين الموجودين
- ✅ عرض قائمة المشرفين مع تفاصيلهم
- ✅ تحديث معلومات المشرف تلقائياً عند دخوله للبوت
- ✅ واجهة تفاعلية سهلة الاستخدام

## 📋 كيفية الاستخدام

### 1. تشغيل الأداة
```bash
python admin_manager.py
```

### 2. إضافة مشرف جديد
1. اختر الخيار `1` (إضافة مشرف جديد)
2. أ<PERSON><PERSON>ل Telegram ID للمشرف
3. أ<PERSON><PERSON><PERSON> اسم المشرف (اختياري)
4. أدخل دور المشرف (افتراضي: مشرف)

**مثال:**
```
🆔 أدخل Telegram ID للمشرف: 123456789
📝 أدخل اسم المشرف (اختياري): أحمد محمد
🎭 أدخل دور المشرف (افتراضي: مشرف): مدير المحتوى
```

### 3. إزالة مشرف
1. اختر الخيار `2` (إزالة مشرف)
2. أدخل Telegram ID للمشرف المراد إزالته
3. أكد العملية بكتابة "نعم"

### 4. عرض قائمة المشرفين
1. اختر الخيار `3` (عرض قائمة المشرفين)
2. ستظهر قائمة بجميع المشرفين مع تفاصيلهم

## 🔄 التحديث التلقائي للمعلومات

عند دخول المشرف للبوت لأول مرة بعد إضافته، سيتم تحديث معلوماته تلقائياً:

- ✅ الاسم الكامل (الاسم الأول + الاسم الأخير)
- ✅ اسم المستخدم (@username)
- ✅ لغة الواجهة
- ✅ تاريخ آخر دخول

## 📊 بنية البيانات في Firebase

### مجموعة المشرفين (admins)
```json
{
  "telegram_id": 123456789,
  "name": "أحمد محمد",
  "username": "ahmed_mohamed",
  "first_name": "أحمد",
  "last_name": "محمد",
  "role": "مدير المحتوى",
  "active": true,
  "created_date": "2025-07-12T04:00:00Z",
  "last_login": "2025-07-12T04:30:00Z",
  "language_code": "ar",
  "permissions": {
    "manage_users": true,
    "manage_content": true,
    "view_statistics": true,
    "manage_ideas": true
  }
}
```

## 🛡️ الصلاحيات الافتراضية

عند إضافة مشرف جديد، يحصل على الصلاحيات التالية:

- ✅ `manage_users`: إدارة المستخدمين
- ✅ `manage_content`: إدارة المحتوى
- ✅ `view_statistics`: عرض الإحصائيات
- ✅ `manage_ideas`: إدارة الأفكار المقترحة

## 🔧 متطلبات التشغيل

1. **Python 3.7+**
2. **Firebase Admin SDK**
3. **ملف `.env` يحتوي على:**
   ```
   FIREBASE_CREDENTIALS_PATH=path/to/firebase-credentials.json
   ```

## 📝 ملاحظات مهمة

- 🔒 **الأمان**: تأكد من أن ملف Firebase credentials محمي ولا يتم مشاركته
- 🔄 **النسخ الاحتياطي**: يتم حفظ جميع البيانات في Firebase تلقائياً
- ⚠️ **التحقق**: الأداة تتحقق من وجود المشرف قبل الإضافة لتجنب التكرار
- 🔍 **المراقبة**: جميع العمليات يتم تسجيلها في logs للمراجعة

## 🆘 استكشاف الأخطاء

### خطأ في الاتصال بـ Firebase
```
❌ خطأ في إضافة المشرف: [Firebase Error]
```
**الحل**: تحقق من:
- صحة ملف Firebase credentials
- الاتصال بالإنترنت
- صلاحيات Firebase

### مشرف موجود مسبقاً
```
⚠️ المشرف 123456789 موجود مسبقاً
```
**الحل**: هذا تحذير طبيعي، المشرف مضاف مسبقاً

## 🔗 الملفات ذات الصلة

- `firebase_manager.py` - إدارة Firebase
- `educational_bot.py` - البوت الرئيسي
- `.env` - متغيرات البيئة

## 📞 الدعم

في حالة وجود مشاكل أو استفسارات، يرجى مراجعة:
1. ملفات الـ logs
2. إعدادات Firebase
3. متغيرات البيئة

---

**تم تطوير هذه الأداة لتسهيل إدارة المشرفين في البوت التعليمي** 🎓
