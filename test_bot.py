#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات البوت التعليمي
Educational Bot Tests

ملف اختبار بسيط للتحقق من عمل البوت
"""

import unittest
from unittest.mock import Mock, patch
import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# استيراد وحدات البوت
try:
    from educational_bot import *
except ImportError as e:
    print(f"خطأ في استيراد البوت: {e}")
    sys.exit(1)

class TestBotFunctions(unittest.TestCase):
    """فئة اختبار وظائف البوت"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.test_user_id = 12345
        self.test_username = "test_user"
    
    def test_user_states(self):
        """اختبار إدارة حالات المستخدمين"""
        # اختبار تعيين الحالة
        set_user_state(self.test_user_id, UserStates.TRANSLATION)
        self.assertEqual(get_user_state(self.test_user_id), UserStates.TRANSLATION)
        
        # اختبار الحالة الافتراضية
        new_user_id = 99999
        self.assertEqual(get_user_state(new_user_id), UserStates.MAIN_MENU)
    
    def test_user_data(self):
        """اختبار إدارة بيانات المستخدمين"""
        # اختبار الحصول على بيانات المستخدم
        user_data_dict = get_user_data(self.test_user_id)
        self.assertIsInstance(user_data_dict, dict)
        
        # اختبار حفظ البيانات
        user_data_dict['test_key'] = 'test_value'
        self.assertEqual(get_user_data(self.test_user_id)['test_key'], 'test_value')
    
    def test_keyboard_creation(self):
        """اختبار إنشاء الكيبورد"""
        # اختبار الكيبورد الرئيسي
        main_keyboard = create_main_keyboard()
        self.assertIsNotNone(main_keyboard)
        
        # اختبار كيبورد العودة
        back_keyboard = create_back_keyboard()
        self.assertIsNotNone(back_keyboard)
    
    def test_educational_content(self):
        """اختبار المحتوى التعليمي"""
        # التحقق من وجود المراحل الدراسية
        self.assertGreater(len(educational_content), 0)
        
        # التحقق من بنية البيانات
        for stage, subjects in educational_content.items():
            self.assertIsInstance(subjects, dict)
            for subject, data in subjects.items():
                self.assertIn('books', data)
                self.assertIn('resources', data)
                self.assertIn('status', data)
    
    def test_ministerial_questions(self):
        """اختبار الأسئلة الوزارية"""
        # التحقق من وجود الأسئلة
        self.assertGreater(len(ministerial_questions), 0)
        
        # التحقق من بنية الأسئلة
        for subject, years in ministerial_questions.items():
            self.assertIsInstance(years, dict)
            for year, data in years.items():
                self.assertIn('questions', data)
                self.assertIn('pdf_link', data)
                
                # التحقق من بنية السؤال
                for question in data['questions']:
                    self.assertIn('question', question)
                    self.assertIn('options', question)
                    self.assertIn('correct', question)
                    self.assertIn('explanation', question)
    
    def test_services_data(self):
        """اختبار بيانات الخدمات"""
        # التحقق من وجود الخدمات
        self.assertGreater(len(services_data), 0)
        
        # التحقق من بنية الخدمات
        for service, data in services_data.items():
            self.assertIn('description', data)
            self.assertIn('specialists', data)
            self.assertIn('contact', data)
    
    def test_helper_functions(self):
        """اختبار الوظائف المساعدة"""
        # اختبار تنسيق الأرقام
        self.assertEqual(format_number(1000), "1,000")
        self.assertEqual(format_number(1234567), "1,234,567")
        
        # اختبار التحقق من المشرفين
        self.assertFalse(is_admin(self.test_user_id))
        if ADMIN_IDS:
            self.assertTrue(is_admin(ADMIN_IDS[0]))

class TestBotConfiguration(unittest.TestCase):
    """فئة اختبار إعدادات البوت"""
    
    def test_bot_token(self):
        """اختبار توكن البوت"""
        self.assertIsNotNone(BOT_TOKEN)
        self.assertNotEqual(BOT_TOKEN, "")
        # التحقق من أن التوكن ليس القيمة الافتراضية
        if BOT_TOKEN != "YOUR_BOT_TOKEN_HERE":
            self.assertGreater(len(BOT_TOKEN), 20)
    
    def test_admin_ids(self):
        """اختبار معرفات المشرفين"""
        self.assertIsInstance(ADMIN_IDS, list)
        # يمكن أن تكون فارغة في البداية
        for admin_id in ADMIN_IDS:
            self.assertIsInstance(admin_id, int)
    
    def test_user_states_enum(self):
        """اختبار تعداد حالات المستخدمين"""
        # التحقق من وجود الحالات المطلوبة
        required_states = [
            'MAIN_MENU', 'TRANSLATION', 'EDUCATIONAL_CONTENT',
            'MINISTERIAL_MATERIALS', 'SERVICES', 'SUGGEST_IDEA'
        ]
        
        for state in required_states:
            self.assertTrue(hasattr(UserStates, state))

def run_basic_tests():
    """تشغيل اختبارات أساسية سريعة"""
    print("🧪 بدء الاختبارات الأساسية...")
    
    # اختبار استيراد الوحدات
    try:
        from educational_bot import bot, educational_content, ministerial_questions, services_data
        print("✅ تم استيراد الوحدات بنجاح")
    except Exception as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        return False
    
    # اختبار البيانات
    tests = [
        (len(educational_content) > 0, "المحتوى التعليمي"),
        (len(ministerial_questions) > 0, "الأسئلة الوزارية"),
        (len(services_data) > 0, "بيانات الخدمات"),
        (BOT_TOKEN != "", "توكن البوت")
    ]
    
    all_passed = True
    for test, name in tests:
        if test:
            print(f"✅ {name}: نجح")
        else:
            print(f"❌ {name}: فشل")
            all_passed = False
    
    return all_passed

def main():
    """الدالة الرئيسية للاختبارات"""
    print("🎓 اختبارات البوت التعليمي")
    print("=" * 40)
    
    # تشغيل الاختبارات الأساسية
    if not run_basic_tests():
        print("\n❌ فشلت الاختبارات الأساسية")
        return
    
    print("\n🧪 تشغيل الاختبارات المتقدمة...")
    
    # تشغيل اختبارات unittest
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # إضافة اختبارات الوظائف
    suite.addTests(loader.loadTestsFromTestCase(TestBotFunctions))
    suite.addTests(loader.loadTestsFromTestCase(TestBotConfiguration))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # عرض النتائج
    print("\n" + "=" * 40)
    if result.wasSuccessful():
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ البوت جاهز للتشغيل")
    else:
        print("❌ بعض الاختبارات فشلت")
        print(f"🔍 الأخطاء: {len(result.errors)}")
        print(f"🔍 الفشل: {len(result.failures)}")

if __name__ == "__main__":
    main()
