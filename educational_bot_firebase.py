#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎓 بوت تيليجرام تعليمي متعدد الأقسام مع Firebase
Educational Telegram Bot with Multiple Sections and Firebase

المطور: Augment Agent
التاريخ: 2025-07-12
الوصف: بوت تعليمي احترافي يحتوي على أقسام متعددة مع قاعدة بيانات Firebase
"""

import telebot
from telebot import types
import json
import os
import logging
from datetime import datetime
import threading
import time
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# استيراد مدير Firebase
from firebase_manager import firebase_manager

# إعداد نظام التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# إعدادات البوت من متغيرات البيئة
BOT_TOKEN = os.getenv('BOT_TOKEN', 'YOUR_BOT_TOKEN_HERE')

# الحصول على معرفات المشرفين من Firebase
try:
    ADMIN_IDS = firebase_manager.get_admin_ids()
    if not ADMIN_IDS:
        # إذا لم توجد معرفات في Firebase، استخدم القيم الافتراضية
        ADMIN_IDS = [123456789]
        logger.warning("⚠️ لم يتم العثور على مشرفين في Firebase، استخدام القيم الافتراضية")
except Exception as e:
    logger.error(f"❌ خطأ في الحصول على المشرفين من Firebase: {e}")
    ADMIN_IDS = [123456789]

# إنشاء كائن البوت
bot = telebot.TeleBot(BOT_TOKEN)

# قاموس لحفظ حالات المستخدمين (مؤقت في الذاكرة)
user_states = {}

# تحميل البيانات من Firebase
def load_data_from_firebase():
    """تحميل البيانات من Firebase"""
    global educational_content, ministerial_questions, services_data
    
    try:
        # تحميل المحتوى التعليمي
        educational_content = firebase_manager.get_educational_content()
        if not educational_content:
            # إذا لم توجد بيانات، استخدم البيانات الافتراضية وحفظها
            educational_content = get_default_educational_content()
            firebase_manager.save_educational_content(educational_content)
        
        # تحميل الأسئلة الوزارية
        ministerial_questions = firebase_manager.get_ministerial_questions()
        if not ministerial_questions:
            ministerial_questions = get_default_ministerial_questions()
            firebase_manager.save_ministerial_questions(ministerial_questions)
        
        # تحميل بيانات الخدمات
        services_data = firebase_manager.get_services_data()
        if not services_data:
            services_data = get_default_services_data()
            firebase_manager.save_services_data(services_data)
        
        logger.info("✅ تم تحميل البيانات من Firebase بنجاح")
        
    except Exception as e:
        logger.error(f"❌ خطأ في تحميل البيانات من Firebase: {e}")
        # استخدام البيانات الافتراضية في حالة الخطأ
        educational_content = get_default_educational_content()
        ministerial_questions = get_default_ministerial_questions()
        services_data = get_default_services_data()

def get_default_educational_content():
    """الحصول على المحتوى التعليمي الافتراضي"""
    return {
        "المرحلة الأولى": {
            "الفيزياء الطبية": {
                "books": ["كتاب الفيزياء الطبية الأساسية", "مبادئ الإشعاع الطبي"],
                "resources": ["ملزمة الفيزياء النووية", "دليل الأمان الإشعاعي"],
                "status": "متوفر"
            },
            "التشريح": {
                "books": ["أطلس التشريح البشري", "علم التشريح التطبيقي"],
                "resources": ["ملزمة التشريح المصورة", "نماذج ثلاثية الأبعاد"],
                "status": "متوفر"
            }
        },
        "المرحلة الثانية": {
            "تقنيات التصوير الطبي": {
                "books": ["أساسيات التصوير الإشعاعي", "تقنيات الأشعة المقطعية"],
                "resources": ["ملزمة التصوير بالرنين المغناطيسي", "حالات سريرية"],
                "status": "متوفر"
            },
            "علم الأمراض": {
                "books": ["علم الأمراض العام", "الأمراض الإشعاعية"],
                "resources": ["أطلس الأمراض المصور", "دراسات حالة"],
                "status": "متوفر"
            }
        },
        "المرحلة الثالثة": {
            "التصوير التشخيصي المتقدم": {
                "books": ["التصوير الطبي المتقدم", "تفسير الصور الإشعاعية"],
                "resources": ["ملزمة التشخيص الإشعاعي", "حالات معقدة"],
                "status": "متوفر"
            },
            "الطب النووي": {
                "books": ["أساسيات الطب النووي", "تطبيقات النظائر المشعة"],
                "resources": ["ملزمة الفحوصات النووية", "بروتوكولات العمل"],
                "status": "متوفر"
            }
        },
        "المرحلة الرابعة": {
            "التدريب السريري": {
                "books": ["دليل التدريب السريري", "إجراءات المستشفى"],
                "resources": ["ملزمة الحالات السريرية", "تقارير التدريب"],
                "status": "متوفر"
            },
            "مشروع التخرج": {
                "books": ["منهجية البحث العلمي", "كتابة الأطروحات"],
                "resources": ["نماذج مشاريع التخرج", "دليل التوثيق"],
                "status": "متوفر"
            }
        }
    }

def get_default_ministerial_questions():
    """الحصول على الأسئلة الوزارية الافتراضية"""
    return {
        "الفيزياء الطبية": {
            "2023": {
                "questions": [
                    {
                        "question": "ما هو مبدأ عمل جهاز الأشعة السينية؟",
                        "options": ["انبعاث الإلكترونات", "التأين الإشعاعي", "الرنين المغناطيسي", "الموجات فوق الصوتية"],
                        "correct": 0,
                        "explanation": "جهاز الأشعة السينية يعمل على مبدأ انبعاث الإلكترونات من الكاثود وضربها للأنود لإنتاج الأشعة السينية"
                    },
                    {
                        "question": "ما هي وحدة قياس الجرعة الإشعاعية؟",
                        "options": ["جراي (Gy)", "سيفرت (Sv)", "بيكريل (Bq)", "كوري (Ci)"],
                        "correct": 0,
                        "explanation": "الجراي (Gy) هي وحدة قياس الجرعة الإشعاعية الممتصة"
                    }
                ],
                "pdf_link": "https://example.com/physics_2023.pdf"
            }
        }
    }

def get_default_services_data():
    """الحصول على بيانات الخدمات الافتراضية"""
    return {
        "التصميم": {
            "description": "خدمات التصميم الجرافيكي والإبداعي",
            "specialists": ["أحمد محمد - مصمم جرافيك", "فاطمة علي - مصممة UI/UX"],
            "contact": "@design_team"
        },
        "البرمجة": {
            "description": "تطوير المواقع والتطبيقات والأنظمة",
            "specialists": ["محمد أحمد - مطور ويب", "سارة حسن - مطورة تطبيقات"],
            "contact": "@programming_team"
        },
        "كتابة المقالات": {
            "description": "كتابة المحتوى والمقالات العلمية والأدبية",
            "specialists": ["نور الدين - كاتب محتوى", "ليلى محمود - محررة"],
            "contact": "@writing_team"
        },
        "إعداد البحوث العلمية": {
            "description": "إعداد وتنسيق البحوث والدراسات العلمية",
            "specialists": ["د. عمر سالم - باحث أكاديمي", "د. هدى كريم - محررة علمية"],
            "contact": "@research_team"
        }
    }

# تحميل البيانات عند بدء التشغيل
load_data_from_firebase()

class UserStates:
    """فئة لتعريف حالات المستخدمين"""
    MAIN_MENU = "main_menu"
    TRANSLATION = "translation"
    EDUCATIONAL_CONTENT = "educational_content"
    MINISTERIAL_MATERIALS = "ministerial_materials"
    SERVICES = "services"
    SUGGEST_IDEA = "suggest_idea"
    WAITING_FOR_TEXT = "waiting_for_text"
    WAITING_FOR_FILE = "waiting_for_file"
    WAITING_FOR_IDEA_NAME = "waiting_for_idea_name"
    WAITING_FOR_IDEA_AUTHOR = "waiting_for_idea_author"
    WAITING_FOR_IDEA_DESCRIPTION = "waiting_for_idea_description"
    WAITING_FOR_IDEA_CATEGORY = "waiting_for_idea_category"

def create_main_keyboard():
    """إنشاء الكيبورد الرئيسي"""
    keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True, row_width=2)
    
    # الأزرار الرئيسية
    btn_translation = types.KeyboardButton("📝 الترجمة")
    btn_content = types.KeyboardButton("📖 المحتوى العلمي")
    btn_ministerial = types.KeyboardButton("🗂️ المواد الوزارية")
    btn_services = types.KeyboardButton("🛠️ الخدمات")
    btn_suggest = types.KeyboardButton("💡 اقتراح فكرة جديدة")
    
    keyboard.add(btn_translation, btn_content)
    keyboard.add(btn_ministerial, btn_services)
    keyboard.add(btn_suggest)
    
    return keyboard

def create_back_keyboard():
    """إنشاء كيبورد العودة للقائمة الرئيسية"""
    keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True)
    btn_back = types.KeyboardButton("🔙 العودة للقائمة الرئيسية")
    keyboard.add(btn_back)
    return keyboard

def get_user_state(user_id):
    """الحصول على حالة المستخدم"""
    return user_states.get(user_id, UserStates.MAIN_MENU)

def set_user_state(user_id, state):
    """تعيين حالة المستخدم"""
    user_states[user_id] = state

def get_user_data(user_id):
    """الحصول على بيانات المستخدم من Firebase"""
    try:
        user_data = firebase_manager.get_user(user_id)
        if user_data is None:
            user_data = {}
        return user_data
    except Exception as e:
        logger.error(f"خطأ في الحصول على بيانات المستخدم {user_id}: {e}")
        return {}

def save_user_data(user_id, data):
    """حفظ بيانات المستخدم في Firebase"""
    try:
        firebase_manager.save_user(user_id, data)
    except Exception as e:
        logger.error(f"خطأ في حفظ بيانات المستخدم {user_id}: {e}")

def log_user_action(user_id, username, action):
    """تسجيل إجراءات المستخدمين"""
    logger.info(f"User {user_id} (@{username}) performed action: {action}")
    
    # حفظ النشاط في Firebase
    try:
        firebase_manager.update_user_activity(user_id, action)
    except Exception as e:
        logger.error(f"خطأ في حفظ نشاط المستخدم: {e}")

@bot.message_handler(commands=['start'])
def start_command(message):
    """معالج أمر البدء"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"
    first_name = message.from_user.first_name or ""
    last_name = message.from_user.last_name or ""
    
    # حفظ بيانات المستخدم في Firebase
    user_data = {
        'telegram_id': user_id,
        'username': username,
        'first_name': first_name,
        'last_name': last_name,
        'join_date': datetime.now(),
        'last_activity': datetime.now()
    }
    save_user_data(user_id, user_data)
    
    log_user_action(user_id, username, "Started bot")
    set_user_state(user_id, UserStates.MAIN_MENU)
    
    welcome_text = """
🎓 **أهلاً وسهلاً بك في البوت التعليمي المتطور!**

مرحباً بك في منصتك التعليمية الشاملة التي تضم:

📝 **الترجمة** - ترجمة الملفات والنصوص من الإنجليزية للعربية
📖 **المحتوى العلمي** - مواد دراسية منظمة حسب المرحلة
🗂️ **المواد الوزارية** - أسئلة وزارية مع الحلول واختبارات تفاعلية
🛠️ **الخدمات** - خدمات متنوعة من فريق المنصة
💡 **اقتراح فكرة جديدة** - شاركنا أفكارك الإبداعية

اختر القسم الذي تريده من الأزرار أدناه ⬇️
"""
    
    bot.send_message(
        message.chat.id,
        welcome_text,
        parse_mode='Markdown',
        reply_markup=create_main_keyboard()
    )

if __name__ == "__main__":
    logger.info("🚀 بدء تشغيل البوت التعليمي مع Firebase...")
    logger.info(f"📊 تم تحميل {len(educational_content)} مراحل دراسية")
    logger.info(f"🗂️ تم تحميل {len(ministerial_questions)} مواد وزارية")
    logger.info(f"🛠️ تم تحميل {len(services_data)} خدمات")
    
    try:
        # إرسال رسالة للمشرفين عند بدء التشغيل
        for admin_id in ADMIN_IDS:
            try:
                bot.send_message(
                    admin_id,
                    "🚀 **تم بدء تشغيل البوت مع Firebase بنجاح!**\n\n📅 " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    parse_mode='Markdown'
                )
            except:
                pass
        
        logger.info("✅ البوت جاهز لاستقبال الرسائل")
        bot.polling(none_stop=True, interval=0, timeout=20)
        
    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        # إرسال إشعار للمشرفين عن الخطأ
        for admin_id in ADMIN_IDS:
            try:
                bot.send_message(
                    admin_id,
                    f"❌ **خطأ في البوت:**\n\n`{str(e)}`",
                    parse_mode='Markdown'
                )
            except:
                pass
