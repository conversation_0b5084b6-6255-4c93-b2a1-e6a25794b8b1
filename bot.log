2025-07-12 03:59:52,429 - firebase_manager - INFO - 📊 تم تحميل 0 مشرف من Firestore
2025-07-12 03:59:52,430 - __main__ - WARNING - ⚠️ لم يتم العثور على مشرفين في Firebase، استخدام القيم الافتراضية
2025-07-12 03:59:52,434 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 03:59:52,435 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 03:59:52,436 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 03:59:52,437 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 03:59:56,900 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:00:01,777 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Started bot
2025-07-12 04:00:01,778 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:00:08,605 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed translation section
2025-07-12 04:00:08,606 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:00:18,524 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed educational content section
2025-07-12 04:00:18,526 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:00:25,970 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed ministerial materials section
2025-07-12 04:00:25,972 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:00:31,495 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed services section
2025-07-12 04:00:31,496 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:00:39,174 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed services section
2025-07-12 04:00:39,177 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:00:40,558 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed suggest idea section
2025-07-12 04:00:40,560 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:01:07,874 - __main__ - ERROR - ❌ خطأ في تشغيل البوت: 'idea'
2025-07-12 04:02:52,746 - firebase_manager - INFO - 📊 تم تحميل 0 مشرف من Firestore
2025-07-12 04:02:52,746 - __main__ - WARNING - ⚠️ لم يتم العثور على مشرفين في Firebase، استخدام القيم الافتراضية
2025-07-12 04:02:52,748 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 04:02:52,749 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:02:52,751 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:02:52,752 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:02:53,811 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:02:57,716 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed translation section
2025-07-12 04:02:57,718 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:03:49,168 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed suggest idea section
2025-07-12 04:03:49,170 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:03:55,597 - __main__ - ERROR - ❌ خطأ في تشغيل البوت: 'idea'
2025-07-12 04:04:39,037 - firebase_manager - INFO - 📊 تم تحميل 0 مشرف من Firestore
2025-07-12 04:04:39,038 - __main__ - WARNING - ⚠️ لم يتم العثور على مشرفين في Firebase، استخدام القيم الافتراضية
2025-07-12 04:04:39,039 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 04:04:39,039 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:04:39,040 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:04:39,040 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:04:40,028 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:04:57,487 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed suggest idea section
2025-07-12 04:04:57,488 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: Object of type datetime is not JSON serializable
2025-07-12 04:05:41,227 - firebase_manager - INFO - 📊 تم تحميل 0 مشرف من Firestore
2025-07-12 04:05:41,230 - __main__ - WARNING - ⚠️ لم يتم العثور على مشرفين في Firebase، استخدام القيم الافتراضية
2025-07-12 04:05:41,233 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 04:05:41,233 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:05:41,234 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:05:41,235 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:05:42,222 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:05:43,250 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:05:43,252 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:05:46,644 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 04:05:46,651 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-12 04:05:54,519 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed suggest idea section
2025-07-12 04:05:56,722 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: 404 No document to update: projects/rbot-e906c/databases/(default)/documents/users/5445116367
2025-07-12 04:06:21,613 - firebase_manager - INFO - 📊 تم تحميل 0 مشرف من Firestore
2025-07-12 04:06:21,614 - __main__ - WARNING - ⚠️ لم يتم العثور على مشرفين في Firebase، استخدام القيم الافتراضية
2025-07-12 04:06:21,616 - __main__ - INFO - �🚀 بدء تشغيل البوت التعليمي...
2025-07-12 04:06:21,617 - __main__ - INFO - 📊 تم تحميل 4 مراحل دراسية
2025-07-12 04:06:21,617 - __main__ - INFO - 🗂️ تم تحميل 1 مواد وزارية
2025-07-12 04:06:21,617 - __main__ - INFO - 🛠️ تم تحميل 4 خدمات
2025-07-12 04:06:22,551 - __main__ - INFO - ✅ البوت جاهز لاستقبال الرسائل
2025-07-12 04:06:29,362 - __main__ - INFO - User 5445116367 (@BBDKB) performed action: Accessed suggest idea section
2025-07-12 04:06:30,963 - firebase_manager - ERROR - ❌ خطأ في تحديث نشاط المستخدم 5445116367: 404 No document to update: projects/rbot-e906c/databases/(default)/documents/users/5445116367
2025-07-12 04:06:47,909 - __main__ - ERROR - ❌ خطأ في تشغيل البوت: 'idea'
