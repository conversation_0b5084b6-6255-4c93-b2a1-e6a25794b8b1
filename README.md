# 🎓 بوت تيليجرام تعليمي متعدد الأقسام

بوت تيليجرام احترافي ومتطور يوفر خدمات تعليمية شاملة للطلاب والمتعلمين.

## ✨ المميزات الرئيسية

### 📝 قسم الترجمة
- ترجمة النصوص من الإنجليزية إلى العربية
- ترجمة الملفات (PDF, Word, TXT)
- ترجمة فورية وعالية الجودة
- دعم المصطلحات الطبية المتخصصة

### 📖 المحتوى العلمي
- تنظيم المحتوى حسب المرحلة الدراسية (4 مراحل)
- مواد دراسية شاملة لكل تخصص
- كتب ومراجع علمية محدثة
- ملازم دراسية مبسطة

### 🗂️ المواد الوزارية
- أسئلة وزارية من السنوات السابقة مع الحلول
- اختبارات تفاعلية داخل البوت
- نتائج فورية مع التقييم
- ملفات PDF قابلة للتحميل
- كورسات فيديو (قيد التطوير)

### 🛠️ الخدمات
- **التصميم**: خدمات التصميم الجرافيكي والإبداعي
- **البرمجة**: تطوير المواقع والتطبيقات
- **كتابة المقالات**: محتوى علمي وأدبي
- **البحوث العلمية**: إعداد وتنسيق البحوث

### 💡 اقتراح الأفكار
- نظام استقبال الأفكار الجديدة من المستخدمين
- تصنيف الأفكار حسب الأقسام
- إشعار المشرفين بالأفكار الجديدة
- متابعة حالة الأفكار المقترحة

## 🚀 التثبيت والتشغيل

### المتطلبات
- Python 3.8 أو أحدث
- توكن بوت تيليجرام

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd bot-0.1
```

2. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

3. **إعداد التوكن**
- احصل على توكن البوت من [@BotFather](https://t.me/BotFather)
- افتح ملف `educational_bot.py`
- استبدل `YOUR_BOT_TOKEN_HERE` بتوكن البوت الخاص بك
- أضف معرفات المشرفين في `ADMIN_IDS`

4. **تشغيل البوت**
```bash
python educational_bot.py
```

## ⚙️ الإعدادات

### إعداد التوكن والمشرفين
```python
BOT_TOKEN = "YOUR_BOT_TOKEN_HERE"  # ضع توكن البوت هنا
ADMIN_IDS = [123456789]  # ضع معرفات المشرفين هنا
```

### إعداد الخدمات
يمكنك تعديل الخدمات المتاحة في متغير `services_data`:
```python
services_data = {
    "اسم الخدمة": {
        "description": "وصف الخدمة",
        "specialists": ["اسم المختص"],
        "contact": "@username"
    }
}
```

## 🎯 الاستخدام

### للمستخدمين العاديين
1. ابدأ محادثة مع البوت
2. اضغط `/start` لبدء الاستخدام
3. اختر القسم المطلوب من الكيبورد الرئيسي
4. اتبع التعليمات التفاعلية

### للمشرفين
- استخدم `/admin` للوصول للوحة التحكم
- عرض الإحصائيات والأفكار المقترحة
- إدارة المستخدمين والمحتوى

## 📊 الميزات الاحترافية

### إشعارات تفاعلية
- إشعارات صغيرة أعلى الشاشة عند الضغط على الأزرار
- رسائل تأكيد للعمليات المهمة
- معالجة احترافية للأخطاء

### نظام التسجيل
- تسجيل جميع إجراءات المستخدمين
- حفظ السجلات في ملف `bot.log`
- إشعار المشرفين عند حدوث أخطاء

### إدارة الحالات
- نظام متقدم لإدارة حالات المستخدمين
- حفظ بيانات المستخدمين مؤقتاً
- انتقال سلس بين الأقسام

## 🔧 التطوير والتخصيص

### إضافة مواد جديدة
```python
educational_content["المرحلة الجديدة"] = {
    "المادة الجديدة": {
        "books": ["كتاب 1", "كتاب 2"],
        "resources": ["مصدر 1", "مصدر 2"],
        "status": "متوفر"
    }
}
```

### إضافة أسئلة وزارية
```python
ministerial_questions["المادة الجديدة"] = {
    "2024": {
        "questions": [
            {
                "question": "نص السؤال",
                "options": ["خيار 1", "خيار 2", "خيار 3", "خيار 4"],
                "correct": 0,
                "explanation": "شرح الإجابة"
            }
        ],
        "pdf_link": "رابط ملف PDF"
    }
}
```

## 📝 ملاحظات مهمة

- البوت يدعم اللغة العربية بالكامل
- جميع الرسائل والواجهات باللغة العربية
- نظام معالجة أخطاء شامل
- تصميم احترافي ومتجاوب
- سهولة في الاستخدام والتنقل

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير البوت:
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى البranch
5. إنشاء Pull Request

## 📞 الدعم

للدعم الفني أو الاستفسارات:
- إنشاء Issue في GitHub
- التواصل مع فريق التطوير

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

**تم التطوير بواسطة:** Augment Agent  
**التاريخ:** 2025-07-12  
**الإصدار:** 1.0.0
