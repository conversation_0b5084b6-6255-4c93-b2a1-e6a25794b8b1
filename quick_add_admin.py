#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة سريعة لإضافة مشرف جديد
استخدم هذا الملف لإضافة مشرف بسرعة دون التفاعل مع القوائم
"""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from firebase_manager import FirebaseManager

def quick_add_admin(telegram_id: int, name: str = None, role: str = "مشرف"):
    """إضافة مشرف بسرعة"""
    try:
        firebase_manager = FirebaseManager()
        
        # التحقق من وجود المشرف مسبقاً
        existing_admins = firebase_manager.get_admin_ids()
        if telegram_id in existing_admins:
            print(f"⚠️ المشرف {telegram_id} موجود مسبقاً")
            return False
        
        # بيانات المشرف
        admin_data = {
            'telegram_id': telegram_id,
            'name': name or f"مشرف_{telegram_id}",
            'role': role,
            'active': True,
            'created_date': datetime.now(),
            'permissions': {
                'manage_users': True,
                'manage_content': True,
                'view_statistics': True,
                'manage_ideas': True
            }
        }
        
        # إضافة المشرف
        success = firebase_manager.add_admin(telegram_id, admin_data)
        
        if success:
            print(f"✅ تم إضافة المشرف {telegram_id} بنجاح")
            print(f"📝 الاسم: {admin_data['name']}")
            print(f"🎭 الدور: {admin_data['role']}")
            return True
        else:
            print(f"❌ فشل في إضافة المشرف {telegram_id}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 أداة الإضافة السريعة للمشرفين")
    print("=" * 40)
    
    # أمثلة على الاستخدام
    examples = [
        {
            'id': 123456789,
            'name': 'أحمد محمد',
            'role': 'مدير المحتوى'
        },
        {
            'id': 987654321,
            'name': 'فاطمة علي',
            'role': 'مشرفة الترجمة'
        }
    ]
    
    print("\n📋 أمثلة على الاستخدام:")
    for i, example in enumerate(examples, 1):
        print(f"{i}. ID: {example['id']}, الاسم: {example['name']}, الدور: {example['role']}")
    
    print("\n" + "=" * 40)
    
    # إدخال بيانات المشرف
    try:
        telegram_id = int(input("🆔 أدخل Telegram ID للمشرف: ").strip())
        name = input("📝 أدخل اسم المشرف (اختياري): ").strip()
        role = input("🎭 أدخل دور المشرف (افتراضي: مشرف): ").strip()
        
        if not name:
            name = None
        if not role:
            role = "مشرف"
        
        print(f"\n🔄 جاري إضافة المشرف...")
        success = quick_add_admin(telegram_id, name, role)
        
        if success:
            print("\n🎉 تمت العملية بنجاح!")
            print("💡 يمكن للمشرف الآن استخدام البوت والحصول على صلاحيات المشرف")
            print("🔄 ستتم إضافة معلوماته الكاملة عند دخوله للبوت لأول مرة")
        else:
            print("\n❌ فشلت العملية!")
            
    except ValueError:
        print("❌ يرجى إدخال رقم صحيح للـ Telegram ID")
    except KeyboardInterrupt:
        print("\n\n👋 تم إلغاء العملية")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()
