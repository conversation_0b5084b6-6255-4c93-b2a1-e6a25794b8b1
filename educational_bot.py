#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎓 بوت تيليجرام تعليمي متعدد الأقسام
Educational Telegram Bot with Multiple Sections

المطور: كرار الحدراوي
التاريخ: 2025-07-12
الوصف: بوت تعليمي احترافي يحتوي على أقسام متعددة للترجمة والمحتوى العلمي والمواد الوزارية والخدمات
"""

import telebot
from telebot import types
import json
import os
import logging
from datetime import datetime
import threading
import time
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# استيراد مدير Firebase
from firebase_manager import firebase_manager

# إعداد نظام التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# إعدادات البوت من متغيرات البيئة
BOT_TOKEN = os.getenv('BOT_TOKEN', 'YOUR_BOT_TOKEN_HERE')

# الحصول على معرفات المشرفين من Firebase
try:
    ADMIN_IDS = firebase_manager.get_admin_ids()
    if not ADMIN_IDS:
        # إذا لم توجد معرفات في Firebase، استخدم القيم الافتراضية
        ADMIN_IDS = [123456789]
        logger.warning("⚠️ لم يتم العثور على مشرفين في Firebase، استخدام القيم الافتراضية")
except Exception as e:
    logger.error(f"❌ خطأ في الحصول على المشرفين من Firebase: {e}")
    ADMIN_IDS = [123456789]

# إنشاء كائن البوت
bot = telebot.TeleBot(BOT_TOKEN)

# قاموس لحفظ حالات المستخدمين (مؤقت في الذاكرة)
user_states = {}

# تحميل البيانات من Firebase
def load_data_from_firebase():
    """تحميل البيانات من Firebase"""
    global educational_content, ministerial_questions, services_data

    try:
        # تحميل المحتوى التعليمي
        educational_content = firebase_manager.get_educational_content()
        if not educational_content:
            # إذا لم توجد بيانات، استخدم البيانات الافتراضية وحفظها
            educational_content = get_default_educational_content()
            firebase_manager.save_educational_content(educational_content)

        # تحميل الأسئلة الوزارية
        ministerial_questions = firebase_manager.get_ministerial_questions()
        if not ministerial_questions:
            ministerial_questions = get_default_ministerial_questions()
            firebase_manager.save_ministerial_questions(ministerial_questions)

        # تحميل بيانات الخدمات
        services_data = firebase_manager.get_services_data()
        if not services_data:
            services_data = get_default_services_data()
            firebase_manager.save_services_data(services_data)

        logger.info("✅ تم تحميل البيانات من Firebase بنجاح")

    except Exception as e:
        logger.error(f"❌ خطأ في تحميل البيانات من Firebase: {e}")
        # استخدام البيانات الافتراضية في حالة الخطأ
        educational_content = get_default_educational_content()
        ministerial_questions = get_default_ministerial_questions()
        services_data = get_default_services_data()

def get_default_educational_content():
    """الحصول على المحتوى التعليمي الافتراضي"""
    return {
        "المرحلة الأولى": {
            "الفيزياء الطبية": {
                "books": ["كتاب الفيزياء الطبية الأساسية", "مبادئ الإشعاع الطبي"],
                "resources": ["ملزمة الفيزياء النووية", "دليل الأمان الإشعاعي"],
                "status": "متوفر"
            },
            "التشريح": {
                "books": ["أطلس التشريح البشري", "علم التشريح التطبيقي"],
                "resources": ["ملزمة التشريح المصورة", "نماذج ثلاثية الأبعاد"],
                "status": "متوفر"
            }
        },
        "المرحلة الثانية": {
            "تقنيات التصوير الطبي": {
                "books": ["أساسيات التصوير الإشعاعي", "تقنيات الأشعة المقطعية"],
                "resources": ["ملزمة التصوير بالرنين المغناطيسي", "حالات سريرية"],
                "status": "متوفر"
            },
            "علم الأمراض": {
                "books": ["علم الأمراض العام", "الأمراض الإشعاعية"],
                "resources": ["أطلس الأمراض المصور", "دراسات حالة"],
                "status": "متوفر"
            }
        },
        "المرحلة الثالثة": {
            "التصوير التشخيصي المتقدم": {
                "books": ["التصوير الطبي المتقدم", "تفسير الصور الإشعاعية"],
                "resources": ["ملزمة التشخيص الإشعاعي", "حالات معقدة"],
                "status": "متوفر"
            },
            "الطب النووي": {
                "books": ["أساسيات الطب النووي", "تطبيقات النظائر المشعة"],
                "resources": ["ملزمة الفحوصات النووية", "بروتوكولات العمل"],
                "status": "متوفر"
            }
        },
        "المرحلة الرابعة": {
            "التدريب السريري": {
                "books": ["دليل التدريب السريري", "إجراءات المستشفى"],
                "resources": ["ملزمة الحالات السريرية", "تقارير التدريب"],
                "status": "متوفر"
            },
            "مشروع التخرج": {
                "books": ["منهجية البحث العلمي", "كتابة الأطروحات"],
                "resources": ["نماذج مشاريع التخرج", "دليل التوثيق"],
                "status": "متوفر"
            }
        }
    }

def get_default_ministerial_questions():
    """الحصول على الأسئلة الوزارية الافتراضية"""
    return {
        "الفيزياء الطبية": {
            "2023": {
                "questions": [
                    {
                        "question": "ما هو مبدأ عمل جهاز الأشعة السينية؟",
                        "options": ["انبعاث الإلكترونات", "التأين الإشعاعي", "الرنين المغناطيسي", "الموجات فوق الصوتية"],
                        "correct": 0,
                        "explanation": "جهاز الأشعة السينية يعمل على مبدأ انبعاث الإلكترونات من الكاثود وضربها للأنود لإنتاج الأشعة السينية"
                    },
                    {
                        "question": "ما هي وحدة قياس الجرعة الإشعاعية؟",
                        "options": ["جراي (Gy)", "سيفرت (Sv)", "بيكريل (Bq)", "كوري (Ci)"],
                        "correct": 0,
                        "explanation": "الجراي (Gy) هي وحدة قياس الجرعة الإشعاعية الممتصة"
                    }
                ],
                "pdf_link": "https://example.com/physics_2023.pdf"
            },
            "2022": {
                "questions": [
                    {
                        "question": "ما هو العنصر المستخدم في أنابيب الأشعة السينية؟",
                        "options": ["التنجستن", "الألمنيوم", "الرصاص", "النحاس"],
                        "correct": 0,
                        "explanation": "التنجستن يستخدم في أنابيب الأشعة السينية لمقاومته العالية للحرارة"
                    }
                ],
                "pdf_link": "https://example.com/physics_2022.pdf"
            }
        }
    }

def get_default_services_data():
    """الحصول على بيانات الخدمات الافتراضية"""
    return {
        "التصميم": {
            "description": "خدمات التصميم الجرافيكي والإبداعي",
            "specialists": ["أحمد محمد - مصمم جرافيك", "فاطمة علي - مصممة UI/UX"],
            "contact": "@design_team"
        },
        "البرمجة": {
            "description": "تطوير المواقع والتطبيقات والأنظمة",
            "specialists": ["محمد أحمد - مطور ويب", "سارة حسن - مطورة تطبيقات"],
            "contact": "@programming_team"
        },
        "كتابة المقالات": {
            "description": "كتابة المحتوى والمقالات العلمية والأدبية",
            "specialists": ["نور الدين - كاتب محتوى", "ليلى محمود - محررة"],
            "contact": "@writing_team"
        },
        "إعداد البحوث العلمية": {
            "description": "إعداد وتنسيق البحوث والدراسات العلمية",
            "specialists": ["د. عمر سالم - باحث أكاديمي", "د. هدى كريم - محررة علمية"],
            "contact": "@research_team"
        }
    }

# قاموس لحفظ الخدمات والمختصين
services_data = {
    "التصميم": {
        "description": "خدمات التصميم الجرافيكي والإبداعي",
        "specialists": ["أحمد محمد - مصمم جرافيك", "فاطمة علي - مصممة UI/UX"],
        "contact": "@design_team"
    },
    "البرمجة": {
        "description": "تطوير المواقع والتطبيقات والأنظمة",
        "specialists": ["محمد أحمد - مطور ويب", "سارة حسن - مطورة تطبيقات"],
        "contact": "@programming_team"
    },
    "كتابة المقالات": {
        "description": "كتابة المحتوى والمقالات العلمية والأدبية",
        "specialists": ["نور الدين - كاتب محتوى", "ليلى محمود - محررة"],
        "contact": "@writing_team"
    },
    "إعداد البحوث العلمية": {
        "description": "إعداد وتنسيق البحوث والدراسات العلمية",
        "specialists": ["د. عمر سالم - باحث أكاديمي", "د. هدى كريم - محررة علمية"],
        "contact": "@research_team"
    }
}

# قاموس للمراحل الدراسية والمواد
educational_content = {
    "المرحلة الأولى": {
        "الفيزياء الطبية": {
            "books": ["كتاب الفيزياء الطبية الأساسية", "مبادئ الإشعاع الطبي"],
            "resources": ["ملزمة الفيزياء النووية", "دليل الأمان الإشعاعي"],
            "status": "متوفر"
        },
        "التشريح": {
            "books": ["أطلس التشريح البشري", "علم التشريح التطبيقي"],
            "resources": ["ملزمة التشريح المصورة", "نماذج ثلاثية الأبعاد"],
            "status": "متوفر"
        }
    },
    "المرحلة الثانية": {
        "تقنيات التصوير الطبي": {
            "books": ["أساسيات التصوير الإشعاعي", "تقنيات الأشعة المقطعية"],
            "resources": ["ملزمة التصوير بالرنين المغناطيسي", "حالات سريرية"],
            "status": "متوفر"
        },
        "علم الأمراض": {
            "books": ["علم الأمراض العام", "الأمراض الإشعاعية"],
            "resources": ["أطلس الأمراض المصور", "دراسات حالة"],
            "status": "متوفر"
        }
    },
    "المرحلة الثالثة": {
        "التصوير التشخيصي المتقدم": {
            "books": ["التصوير الطبي المتقدم", "تفسير الصور الإشعاعية"],
            "resources": ["ملزمة التشخيص الإشعاعي", "حالات معقدة"],
            "status": "متوفر"
        },
        "الطب النووي": {
            "books": ["أساسيات الطب النووي", "تطبيقات النظائر المشعة"],
            "resources": ["ملزمة الفحوصات النووية", "بروتوكولات العمل"],
            "status": "متوفر"
        }
    },
    "المرحلة الرابعة": {
        "التدريب السريري": {
            "books": ["دليل التدريب السريري", "إجراءات المستشفى"],
            "resources": ["ملزمة الحالات السريرية", "تقارير التدريب"],
            "status": "متوفر"
        },
        "مشروع التخرج": {
            "books": ["منهجية البحث العلمي", "كتابة الأطروحات"],
            "resources": ["نماذج مشاريع التخرج", "دليل التوثيق"],
            "status": "متوفر"
        }
    }
}

# قاموس للأسئلة الوزارية
ministerial_questions = {
    "الفيزياء الطبية": {
        "2023": {
            "questions": [
                {
                    "question": "ما هو مبدأ عمل جهاز الأشعة السينية؟",
                    "options": ["انبعاث الإلكترونات", "التأين الإشعاعي", "الرنين المغناطيسي", "الموجات فوق الصوتية"],
                    "correct": 0,
                    "explanation": "جهاز الأشعة السينية يعمل على مبدأ انبعاث الإلكترونات من الكاثود وضربها للأنود لإنتاج الأشعة السينية"
                },
                {
                    "question": "ما هي وحدة قياس الجرعة الإشعاعية؟",
                    "options": ["جراي (Gy)", "سيفرت (Sv)", "بيكريل (Bq)", "كوري (Ci)"],
                    "correct": 0,
                    "explanation": "الجراي (Gy) هي وحدة قياس الجرعة الإشعاعية الممتصة"
                }
            ],
            "pdf_link": "https://example.com/physics_2023.pdf"
        },
        "2022": {
            "questions": [
                {
                    "question": "ما هو العنصر المستخدم في أنابيب الأشعة السينية؟",
                    "options": ["التنجستن", "الألمنيوم", "الرصاص", "النحاس"],
                    "correct": 0,
                    "explanation": "التنجستن يستخدم في أنابيب الأشعة السينية لمقاومته العالية للحرارة"
                }
            ],
            "pdf_link": "https://example.com/physics_2022.pdf"
        }
    }
}

class UserStates:
    """فئة لتعريف حالات المستخدمين"""
    MAIN_MENU = "main_menu"
    TRANSLATION = "translation"
    EDUCATIONAL_CONTENT = "educational_content"
    MINISTERIAL_MATERIALS = "ministerial_materials"
    SERVICES = "services"
    SUGGEST_IDEA = "suggest_idea"
    WAITING_FOR_TEXT = "waiting_for_text"
    WAITING_FOR_FILE = "waiting_for_file"
    WAITING_FOR_IDEA_NAME = "waiting_for_idea_name"
    WAITING_FOR_IDEA_AUTHOR = "waiting_for_idea_author"
    WAITING_FOR_IDEA_DESCRIPTION = "waiting_for_idea_description"
    WAITING_FOR_IDEA_CATEGORY = "waiting_for_idea_category"

def create_main_keyboard():
    """إنشاء الكيبورد الرئيسي"""
    keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True, row_width=2)
    
    # الأزرار الرئيسية
    btn_translation = types.KeyboardButton("📝 الترجمة")
    btn_content = types.KeyboardButton("📖 المحتوى العلمي")
    btn_ministerial = types.KeyboardButton("🗂️ المواد الوزارية")
    btn_services = types.KeyboardButton("🛠️ الخدمات")
    btn_suggest = types.KeyboardButton("💡 اقتراح فكرة جديدة")
    
    keyboard.add(btn_translation, btn_content)
    keyboard.add(btn_ministerial, btn_services)
    keyboard.add(btn_suggest)
    
    return keyboard

def create_back_keyboard():
    """إنشاء كيبورد العودة للقائمة الرئيسية"""
    keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True)
    btn_back = types.KeyboardButton("🔙 العودة للقائمة الرئيسية")
    keyboard.add(btn_back)
    return keyboard

def get_user_state(user_id):
    """الحصول على حالة المستخدم"""
    return user_states.get(user_id, UserStates.MAIN_MENU)

def set_user_state(user_id, state):
    """تعيين حالة المستخدم"""
    user_states[user_id] = state

def get_user_data(user_id):
    """الحصول على بيانات المستخدم من Firebase"""
    try:
        user_data = firebase_manager.get_user(user_id)
        if user_data is None:
            user_data = {}
        return user_data
    except Exception as e:
        logger.error(f"خطأ في الحصول على بيانات المستخدم {user_id}: {e}")
        return {}

def log_user_action(user_id, username, action):
    """تسجيل إجراءات المستخدمين"""
    logger.info(f"User {user_id} (@{username}) performed action: {action}")

    # حفظ النشاط في Firebase
    try:
        firebase_manager.update_user_activity(user_id, action)
    except Exception as e:
        logger.error(f"خطأ في حفظ نشاط المستخدم: {e}")

def save_user_data(user_id, data):
    """حفظ بيانات المستخدم في Firebase"""
    try:
        firebase_manager.save_user(user_id, data)
    except Exception as e:
        logger.error(f"خطأ في حفظ بيانات المستخدم {user_id}: {e}")

@bot.message_handler(commands=['start'])
def start_command(message):
    """معالج أمر البدء"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"
    
    log_user_action(user_id, username, "Started bot")
    set_user_state(user_id, UserStates.MAIN_MENU)
    
    welcome_text = """
🎓 **أهلاً وسهلاً بك في البوت التعليمي المتطور!**

مرحباً بك في منصتك التعليمية الشاملة التي تضم:

📝 **الترجمة** - ترجمة الملفات والنصوص من الإنجليزية للعربية
📖 **المحتوى العلمي** - مواد دراسية منظمة حسب المرحلة
🗂️ **المواد الوزارية** - أسئلة وزارية مع الحلول واختبارات تفاعلية
🛠️ **الخدمات** - خدمات متنوعة من فريق المنصة
💡 **اقتراح فكرة جديدة** - شاركنا أفكارك الإبداعية

اختر القسم الذي تريده من الأزرار أدناه ⬇️
"""
    
    bot.send_message(
        message.chat.id,
        welcome_text,
        parse_mode='Markdown',
        reply_markup=create_main_keyboard()
    )

@bot.message_handler(commands=['help'])
def help_command(message):
    """معالج أمر المساعدة"""
    help_text = """
🆘 **دليل استخدام البوت**

**الأوامر المتاحة:**
/start - بدء استخدام البوت
/help - عرض هذه المساعدة
/status - حالة البوت

**الأقسام المتاحة:**
📝 الترجمة - ترجمة فورية للنصوص والملفات
📖 المحتوى العلمي - مواد دراسية شاملة
🗂️ المواد الوزارية - أسئلة وزارية واختبارات
🛠️ الخدمات - خدمات احترافية متنوعة
💡 اقتراح الأفكار - شاركنا اقتراحاتك

**للدعم الفني:** @support_team
"""
    
    bot.send_message(message.chat.id, help_text, parse_mode='Markdown')

@bot.message_handler(commands=['status'])
def status_command(message):
    """معالج أمر حالة البوت"""
    user_id = message.from_user.id
    if user_id in ADMIN_IDS:
        try:
            stats = firebase_manager.get_bot_statistics()
            status_text = f"""
📊 **حالة البوت**

🟢 البوت يعمل بشكل طبيعي
👥 عدد المستخدمين: {stats.get('total_users', 0)}
👥 النشطين حالياً: {len(user_states)}
💡 عدد الأفكار المقترحة: {stats.get('total_ideas', 0)}
🎯 عدد الاختبارات: {stats.get('total_tests', 0)}
👨‍💼 المشرفين النشطين: {stats.get('active_admins', 0)}
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات: {e}")
            status_text = f"""
📊 **حالة البوت**

🟢 البوت يعمل بشكل طبيعي
👥 النشطين حالياً: {len(user_states)}
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        bot.send_message(message.chat.id, status_text, parse_mode='Markdown')
    else:
        bot.send_message(message.chat.id, "❌ هذا الأمر متاح للمشرفين فقط")

@bot.message_handler(func=lambda message: message.text == "🔙 العودة للقائمة الرئيسية")
def back_to_main(message):
    """العودة للقائمة الرئيسية"""
    user_id = message.from_user.id
    set_user_state(user_id, UserStates.MAIN_MENU)

    bot.send_message(
        message.chat.id,
        "🏠 تم العودة للقائمة الرئيسية",
        reply_markup=create_main_keyboard()
    )

@bot.message_handler(func=lambda message: message.text == "📝 الترجمة")
def translation_section(message):
    """قسم الترجمة"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    log_user_action(user_id, username, "Accessed translation section")
    set_user_state(user_id, UserStates.TRANSLATION)

    keyboard = types.InlineKeyboardMarkup(row_width=1)
    btn_text = types.InlineKeyboardButton("📄 ترجمة نص", callback_data="translate_text")
    btn_file = types.InlineKeyboardButton("📎 ترجمة ملف", callback_data="translate_file")
    btn_back = types.InlineKeyboardButton("🔙 العودة", callback_data="back_main")

    keyboard.add(btn_text, btn_file, btn_back)

    translation_text = """
📝 **قسم الترجمة**

مرحباً بك في قسم الترجمة المتطور!

🔹 **الخدمات المتاحة:**
• ترجمة النصوص من الإنجليزية إلى العربية
• ترجمة الملفات (PDF, Word, TXT)
• ترجمة فورية وعالية الجودة

🔹 **المميزات:**
• دقة عالية في الترجمة
• الحفاظ على التنسيق الأصلي
• ترجمة المصطلحات الطبية المتخصصة

اختر نوع الترجمة المطلوبة:
"""

    bot.send_message(
        message.chat.id,
        translation_text,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

@bot.message_handler(func=lambda message: message.text == "📖 المحتوى العلمي")
def educational_content_section(message):
    """قسم المحتوى العلمي"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    log_user_action(user_id, username, "Accessed educational content section")
    set_user_state(user_id, UserStates.EDUCATIONAL_CONTENT)

    keyboard = types.InlineKeyboardMarkup(row_width=2)

    # إضافة أزرار المراحل الدراسية
    for stage in educational_content.keys():
        btn = types.InlineKeyboardButton(stage, callback_data=f"stage_{stage}")
        keyboard.add(btn)

    btn_back = types.InlineKeyboardButton("🔙 العودة", callback_data="back_main")
    keyboard.add(btn_back)

    content_text = """
📖 **المحتوى العلمي**

مرحباً بك في مكتبة المحتوى العلمي الشاملة!

🔹 **ما نوفره لك:**
• كتب ومراجع علمية محدثة
• ملازم دراسية مبسطة
• مصادر تعليمية متنوعة
• محتوى منظم حسب المرحلة الدراسية

🔹 **التخصصات المتاحة:**
• الفيزياء الطبية
• تقنيات التصوير الطبي
• التشريح وعلم الأمراض
• الطب النووي والتشخيص الإشعاعي

اختر المرحلة الدراسية:
"""

    bot.send_message(
        message.chat.id,
        content_text,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

@bot.message_handler(func=lambda message: message.text == "🗂️ المواد الوزارية")
def ministerial_materials_section(message):
    """قسم المواد الوزارية"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    log_user_action(user_id, username, "Accessed ministerial materials section")
    set_user_state(user_id, UserStates.MINISTERIAL_MATERIALS)

    keyboard = types.InlineKeyboardMarkup(row_width=1)
    btn_questions = types.InlineKeyboardButton("📋 الأسئلة الوزارية", callback_data="ministerial_questions")
    btn_interactive = types.InlineKeyboardButton("🎯 اختبار تفاعلي", callback_data="interactive_test")
    btn_courses = types.InlineKeyboardButton("🎥 الكورسات المرئية", callback_data="video_courses")
    btn_back = types.InlineKeyboardButton("🔙 العودة", callback_data="back_main")

    keyboard.add(btn_questions, btn_interactive, btn_courses, btn_back)

    ministerial_text = """
🗂️ **المواد الوزارية**

مرحباً بك في قسم المواد الوزارية الشامل!

🔹 **ما نوفره لك:**
• أسئلة وزارية من السنوات السابقة مع الحلول
• اختبارات تفاعلية لقياس مستواك
• ملازم وزارية مترجمة وغير مترجمة
• كورسات فيديو تعليمية (قيد التطوير)

🔹 **المميزات:**
• حلول مفصلة ومشروحة
• تصنيف حسب المادة والسنة
• اختبارات تفاعلية مع النتائج الفورية
• محتوى محدث باستمرار

اختر الخدمة المطلوبة:
"""

    bot.send_message(
        message.chat.id,
        ministerial_text,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

@bot.message_handler(func=lambda message: message.text == "🛠️ الخدمات")
def services_section(message):
    """قسم الخدمات"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    log_user_action(user_id, username, "Accessed services section")
    set_user_state(user_id, UserStates.SERVICES)

    keyboard = types.InlineKeyboardMarkup(row_width=2)

    # إضافة أزرار الخدمات
    for service_name in services_data.keys():
        btn = types.InlineKeyboardButton(service_name, callback_data=f"service_{service_name}")
        keyboard.add(btn)

    btn_back = types.InlineKeyboardButton("🔙 العودة", callback_data="back_main")
    keyboard.add(btn_back)

    services_text = """
🛠️ **الخدمات**

مرحباً بك في قسم الخدمات الاحترافية!

🔹 **الخدمات المتاحة:**
• 🎨 التصميم الجرافيكي والإبداعي
• 💻 البرمجة وتطوير التطبيقات
• ✍️ كتابة المقالات والمحتوى
• 🔬 إعداد البحوث العلمية

🔹 **مميزات خدماتنا:**
• فريق متخصص ومحترف
• جودة عالية وأسعار منافسة
• تسليم في الوقت المحدد
• دعم فني مستمر

اختر الخدمة المطلوبة لمعرفة التفاصيل:
"""

    bot.send_message(
        message.chat.id,
        services_text,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

@bot.message_handler(func=lambda message: message.text == "💡 اقتراح فكرة جديدة")
def suggest_idea_section(message):
    """قسم اقتراح الأفكار"""
    user_id = message.from_user.id
    username = message.from_user.username or "Unknown"

    log_user_action(user_id, username, "Accessed suggest idea section")
    set_user_state(user_id, UserStates.WAITING_FOR_IDEA_NAME)

    # تهيئة بيانات المستخدم
    user_data_dict = get_user_data(user_id)
    user_data_dict['idea'] = {}

    idea_text = """
💡 **اقتراح فكرة جديدة**

نحن نقدر أفكارك الإبداعية ونسعى لتطوير المنصة باستمرار!

🔹 **ما نحتاجه منك:**
• اسم الفكرة
• اسم صاحب الفكرة
• وصف مفصل للفكرة
• القسم المناسب لها

📝 **ابدأ بكتابة اسم الفكرة:**
"""

    bot.send_message(
        message.chat.id,
        idea_text,
        parse_mode='Markdown',
        reply_markup=create_back_keyboard()
    )

# معالجات الـ Callback Queries
@bot.callback_query_handler(func=lambda call: True)
def handle_callback_query(call):
    """معالج الـ callback queries"""
    user_id = call.from_user.id
    data = call.data

    # إشعار احترافي للمستخدم
    bot.answer_callback_query(call.id, "تم اختيار هذا القسم ✅")

    try:
        if data == "back_main":
            # العودة للقائمة الرئيسية
            set_user_state(user_id, UserStates.MAIN_MENU)
            bot.edit_message_text(
                "🏠 تم العودة للقائمة الرئيسية",
                call.message.chat.id,
                call.message.message_id,
                reply_markup=None
            )
            bot.send_message(
                call.message.chat.id,
                "اختر القسم المطلوب:",
                reply_markup=create_main_keyboard()
            )

        elif data == "translate_text":
            # ترجمة نص
            set_user_state(user_id, UserStates.WAITING_FOR_TEXT)
            bot.edit_message_text(
                "📝 **ترجمة النص**\n\nأرسل النص الذي تريد ترجمته من الإنجليزية إلى العربية:",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )

        elif data == "translate_file":
            # ترجمة ملف
            set_user_state(user_id, UserStates.WAITING_FOR_FILE)
            bot.edit_message_text(
                "📎 **ترجمة الملف**\n\nأرسل الملف الذي تريد ترجمته (PDF, Word, TXT):",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )

        elif data.startswith("stage_"):
            # عرض محتوى المرحلة الدراسية
            stage_name = data.replace("stage_", "")
            show_stage_content(call, stage_name)

        elif data == "ministerial_questions":
            # عرض الأسئلة الوزارية
            show_ministerial_questions(call)

        elif data == "interactive_test":
            # بدء اختبار تفاعلي
            start_interactive_test(call)

        elif data == "video_courses":
            # عرض الكورسات المرئية
            show_video_courses(call)

        elif data.startswith("service_"):
            # عرض تفاصيل الخدمة
            service_name = data.replace("service_", "")
            show_service_details(call, service_name)

        elif data.startswith("subject_"):
            # عرض محتوى المادة
            subject_info = data.replace("subject_", "").split("_")
            stage = subject_info[0]
            subject = "_".join(subject_info[1:])
            show_subject_content(call, stage, subject)

        elif data.startswith("questions_"):
            # عرض أسئلة المادة
            subject = data.replace("questions_", "")
            show_subject_questions(call, subject)

        elif data.startswith("year_"):
            # عرض أسئلة سنة معينة
            info = data.replace("year_", "").split("_")
            subject = info[0]
            year = info[1]
            show_year_questions(call, subject, year)

        elif data.startswith("test_"):
            # بدء اختبار لمادة معينة
            subject = data.replace("test_", "")
            start_subject_test(call, subject)

        elif data.startswith("answer_"):
            # معالجة إجابة السؤال
            handle_test_answer(call, data)

    except Exception as e:
        logger.error(f"خطأ في معالجة callback query: {e}")
        bot.answer_callback_query(call.id, "حدث خطأ، يرجى المحاولة مرة أخرى ❌")

def show_stage_content(call, stage_name):
    """عرض محتوى المرحلة الدراسية"""
    if stage_name in educational_content:
        keyboard = types.InlineKeyboardMarkup(row_width=1)

        stage_data = educational_content[stage_name]
        for subject_name in stage_data.keys():
            btn = types.InlineKeyboardButton(
                f"📚 {subject_name}",
                callback_data=f"subject_{stage_name}_{subject_name}"
            )
            keyboard.add(btn)

        btn_back = types.InlineKeyboardButton("🔙 العودة", callback_data="back_main")
        keyboard.add(btn_back)

        content_text = f"""
📖 **{stage_name}**

المواد المتاحة في هذه المرحلة:

"""
        for subject_name, subject_data in stage_data.items():
            content_text += f"📚 **{subject_name}** - {subject_data['status']}\n"

        content_text += "\nاختر المادة لعرض محتواها:"

        bot.edit_message_text(
            content_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

def show_subject_content(call, stage, subject):
    """عرض محتوى المادة"""
    if stage in educational_content and subject in educational_content[stage]:
        subject_data = educational_content[stage][subject]

        keyboard = types.InlineKeyboardMarkup(row_width=1)
        btn_back = types.InlineKeyboardButton("🔙 العودة للمرحلة", callback_data=f"stage_{stage}")
        btn_main = types.InlineKeyboardButton("🏠 القائمة الرئيسية", callback_data="back_main")
        keyboard.add(btn_back, btn_main)

        content_text = f"""
📚 **{subject}**
🎓 المرحلة: {stage}

📖 **الكتب المتاحة:**
"""
        for book in subject_data['books']:
            content_text += f"• {book}\n"

        content_text += "\n📋 **المصادر الإضافية:**\n"
        for resource in subject_data['resources']:
            content_text += f"• {resource}\n"

        content_text += f"\n✅ **الحالة:** {subject_data['status']}"

        bot.edit_message_text(
            content_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

def show_ministerial_questions(call):
    """عرض الأسئلة الوزارية"""
    keyboard = types.InlineKeyboardMarkup(row_width=1)

    for subject in ministerial_questions.keys():
        btn = types.InlineKeyboardButton(
            f"📋 {subject}",
            callback_data=f"questions_{subject}"
        )
        keyboard.add(btn)

    btn_back = types.InlineKeyboardButton("🔙 العودة", callback_data="back_main")
    keyboard.add(btn_back)

    questions_text = """
📋 **الأسئلة الوزارية**

اختر المادة لعرض الأسئلة الوزارية الخاصة بها:

🔹 **المتاح حالياً:**
• أسئلة من السنوات السابقة
• حلول مفصلة ومشروحة
• ملفات PDF قابلة للتحميل
"""

    bot.edit_message_text(
        questions_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

def show_subject_questions(call, subject):
    """عرض أسئلة المادة"""
    if subject in ministerial_questions:
        keyboard = types.InlineKeyboardMarkup(row_width=2)

        subject_data = ministerial_questions[subject]
        for year in subject_data.keys():
            btn = types.InlineKeyboardButton(
                f"📅 {year}",
                callback_data=f"year_{subject}_{year}"
            )
            keyboard.add(btn)

        btn_back = types.InlineKeyboardButton("🔙 العودة", callback_data="ministerial_questions")
        keyboard.add(btn_back)

        questions_text = f"""
📋 **أسئلة {subject}**

السنوات المتاحة:

اختر السنة لعرض الأسئلة:
"""

        bot.edit_message_text(
            questions_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

def show_year_questions(call, subject, year):
    """عرض أسئلة سنة معينة"""
    if subject in ministerial_questions and year in ministerial_questions[subject]:
        year_data = ministerial_questions[subject][year]

        keyboard = types.InlineKeyboardMarkup(row_width=1)
        btn_pdf = types.InlineKeyboardButton("📄 تحميل PDF", url=year_data['pdf_link'])
        btn_test = types.InlineKeyboardButton("🎯 اختبار تفاعلي", callback_data=f"test_{subject}_{year}")
        btn_back = types.InlineKeyboardButton("🔙 العودة", callback_data=f"questions_{subject}")

        keyboard.add(btn_pdf, btn_test, btn_back)

        questions_text = f"""
📋 **أسئلة {subject} - {year}**

📊 **إحصائيات:**
• عدد الأسئلة: {len(year_data['questions'])}
• نوع الأسئلة: اختيار من متعدد
• مع الحلول المفصلة

🔹 **الخيارات المتاحة:**
• تحميل ملف PDF
• اختبار تفاعلي داخل البوت

اختر الطريقة المفضلة لديك:
"""

        bot.edit_message_text(
            questions_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

def start_subject_test(call, test_info):
    """بدء اختبار لمادة معينة"""
    info_parts = test_info.split("_")
    if len(info_parts) >= 2:
        subject = info_parts[0]
        year = info_parts[1]

        if subject in ministerial_questions and year in ministerial_questions[subject]:
            user_id = call.from_user.id
            user_data_dict = get_user_data(user_id)

            # تهيئة بيانات الاختبار
            user_data_dict['test'] = {
                'subject': subject,
                'year': year,
                'questions': ministerial_questions[subject][year]['questions'],
                'current_question': 0,
                'score': 0,
                'answers': []
            }

            show_test_question(call, user_id)

def show_test_question(call, user_id):
    """عرض سؤال الاختبار"""
    user_data_dict = get_user_data(user_id)
    test_data = user_data_dict.get('test', {})

    if not test_data:
        return

    questions = test_data['questions']
    current_q = test_data['current_question']

    if current_q < len(questions):
        question = questions[current_q]

        keyboard = types.InlineKeyboardMarkup(row_width=1)

        for i, option in enumerate(question['options']):
            btn = types.InlineKeyboardButton(
                f"{chr(65+i)}. {option}",
                callback_data=f"answer_{current_q}_{i}"
            )
            keyboard.add(btn)

        question_text = f"""
🎯 **اختبار {test_data['subject']} - {test_data['year']}**

📊 السؤال {current_q + 1} من {len(questions)}
📈 النقاط الحالية: {test_data['score']}/{current_q}

❓ **{question['question']}**

اختر الإجابة الصحيحة:
"""

        bot.edit_message_text(
            question_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )
    else:
        # انتهاء الاختبار
        show_test_results(call, user_id)

def handle_test_answer(call, data):
    """معالجة إجابة السؤال"""
    user_id = call.from_user.id
    user_data_dict = get_user_data(user_id)
    test_data = user_data_dict.get('test', {})

    if not test_data:
        return

    # استخراج معلومات الإجابة
    parts = data.replace("answer_", "").split("_")
    question_num = int(parts[0])
    answer_choice = int(parts[1])

    questions = test_data['questions']
    current_question = questions[question_num]

    # تسجيل الإجابة
    is_correct = answer_choice == current_question['correct']
    test_data['answers'].append({
        'question': question_num,
        'answer': answer_choice,
        'correct': is_correct
    })

    if is_correct:
        test_data['score'] += 1
        bot.answer_callback_query(call.id, "✅ إجابة صحيحة!")
    else:
        bot.answer_callback_query(call.id, "❌ إجابة خاطئة")

    # الانتقال للسؤال التالي
    test_data['current_question'] += 1
    show_test_question(call, user_id)

def show_test_results(call, user_id):
    """عرض نتائج الاختبار"""
    user_data_dict = get_user_data(user_id)
    test_data = user_data_dict.get('test', {})

    if not test_data:
        return

    total_questions = len(test_data['questions'])
    score = test_data['score']
    percentage = (score / total_questions) * 100

    # تحديد التقييم
    if percentage >= 90:
        grade = "ممتاز 🌟"
        emoji = "🎉"
    elif percentage >= 80:
        grade = "جيد جداً 👍"
        emoji = "👏"
    elif percentage >= 70:
        grade = "جيد 👌"
        emoji = "😊"
    elif percentage >= 60:
        grade = "مقبول 📚"
        emoji = "🤔"
    else:
        grade = "يحتاج تحسين 📖"
        emoji = "💪"

    keyboard = types.InlineKeyboardMarkup(row_width=1)
    btn_retry = types.InlineKeyboardButton("🔄 إعادة الاختبار", callback_data=f"test_{test_data['subject']}_{test_data['year']}")
    btn_back = types.InlineKeyboardButton("🔙 العودة", callback_data="ministerial_questions")
    btn_main = types.InlineKeyboardButton("🏠 القائمة الرئيسية", callback_data="back_main")

    keyboard.add(btn_retry, btn_back, btn_main)

    results_text = f"""
{emoji} **نتائج الاختبار**

📊 **الإحصائيات:**
• المادة: {test_data['subject']}
• السنة: {test_data['year']}
• النتيجة: {score}/{total_questions}
• النسبة المئوية: {percentage:.1f}%
• التقييم: {grade}

📈 **تفاصيل الأداء:**
"""

    for i, answer in enumerate(test_data['answers']):
        question = test_data['questions'][answer['question']]
        status = "✅" if answer['correct'] else "❌"
        results_text += f"{status} السؤال {i+1}: {question['question'][:50]}...\n"

    results_text += "\n🎯 استمر في التدريب لتحسين نتائجك!"

    bot.edit_message_text(
        results_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

def start_interactive_test(call):
    """بدء اختبار تفاعلي عام"""
    keyboard = types.InlineKeyboardMarkup(row_width=1)

    for subject in ministerial_questions.keys():
        btn = types.InlineKeyboardButton(
            f"🎯 اختبار {subject}",
            callback_data=f"test_{subject}"
        )
        keyboard.add(btn)

    btn_back = types.InlineKeyboardButton("🔙 العودة", callback_data="back_main")
    keyboard.add(btn_back)

    test_text = """
🎯 **الاختبارات التفاعلية**

اختر المادة التي تريد اختبار نفسك فيها:

🔹 **مميزات الاختبار:**
• أسئلة من الامتحانات الوزارية
• تصحيح فوري مع الشرح
• نتائج مفصلة
• إمكانية إعادة الاختبار

اختر المادة:
"""

    bot.edit_message_text(
        test_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

def show_video_courses(call):
    """عرض الكورسات المرئية"""
    keyboard = types.InlineKeyboardMarkup(row_width=1)
    btn_back = types.InlineKeyboardButton("🔙 العودة", callback_data="back_main")
    keyboard.add(btn_back)

    courses_text = """
🎥 **الكورسات المرئية**

🚧 **هذا القسم قيد التطوير**

🔹 **ما سيتوفر قريباً:**
• فيديوهات تعليمية شاملة
• شرح مفصل لكل مادة
• نصوص المحاضرات
• اختبارات بعد كل محاضرة
• 10 أسئلة MCQ عشوائية

📅 **موعد الإطلاق:** قريباً جداً
🔔 **سيتم إشعارك عند توفر المحتوى**

شكراً لصبركم! 🙏
"""

    bot.edit_message_text(
        courses_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

def show_service_details(call, service_name):
    """عرض تفاصيل الخدمة"""
    if service_name in services_data:
        service = services_data[service_name]

        keyboard = types.InlineKeyboardMarkup(row_width=1)
        btn_contact = types.InlineKeyboardButton(
            f"📞 التواصل مع الفريق",
            url=f"https://t.me/{service['contact'].replace('@', '')}"
        )
        btn_back = types.InlineKeyboardButton("🔙 العودة للخدمات", callback_data="back_main")

        keyboard.add(btn_contact, btn_back)

        service_text = f"""
🛠️ **{service_name}**

📝 **الوصف:**
{service['description']}

👥 **المختصون:**
"""
        for specialist in service['specialists']:
            service_text += f"• {specialist}\n"

        service_text += f"""
📞 **للتواصل:** {service['contact']}

🔹 **مميزات الخدمة:**
• جودة عالية ومعايير احترافية
• فريق متخصص وذو خبرة
• أسعار منافسة ومناسبة
• تسليم في الوقت المحدد
• دعم فني مستمر

اضغط على زر التواصل للحصول على عرض سعر مخصص!
"""

        bot.edit_message_text(
            service_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown',
            reply_markup=keyboard
        )

# معالجات الرسائل النصية والملفات
@bot.message_handler(content_types=['text'])
def handle_text_messages(message):
    """معالج الرسائل النصية"""
    user_id = message.from_user.id
    user_state = get_user_state(user_id)
    text = message.text

    if user_state == UserStates.WAITING_FOR_TEXT:
        # ترجمة النص
        translate_text(message, text)

    elif user_state == UserStates.WAITING_FOR_IDEA_NAME:
        # حفظ اسم الفكرة
        user_data_dict = get_user_data(user_id)
        user_data_dict['idea']['name'] = text
        set_user_state(user_id, UserStates.WAITING_FOR_IDEA_AUTHOR)

        bot.send_message(
            message.chat.id,
            "✅ تم حفظ اسم الفكرة\n\n👤 الآن اكتب اسم صاحب الفكرة:",
            reply_markup=create_back_keyboard()
        )

    elif user_state == UserStates.WAITING_FOR_IDEA_AUTHOR:
        # حفظ اسم صاحب الفكرة
        user_data_dict = get_user_data(user_id)
        user_data_dict['idea']['author'] = text
        set_user_state(user_id, UserStates.WAITING_FOR_IDEA_DESCRIPTION)

        bot.send_message(
            message.chat.id,
            "✅ تم حفظ اسم صاحب الفكرة\n\n📝 الآن اكتب وصف مفصل للفكرة:",
            reply_markup=create_back_keyboard()
        )

    elif user_state == UserStates.WAITING_FOR_IDEA_DESCRIPTION:
        # حفظ وصف الفكرة
        user_data_dict = get_user_data(user_id)
        user_data_dict['idea']['description'] = text
        set_user_state(user_id, UserStates.WAITING_FOR_IDEA_CATEGORY)

        # عرض الأقسام المتاحة
        keyboard = types.InlineKeyboardMarkup(row_width=1)
        categories = ["الترجمة", "المحتوى العلمي", "المواد الوزارية", "الخدمات", "قسم جديد"]

        for category in categories:
            btn = types.InlineKeyboardButton(category, callback_data=f"category_{category}")
            keyboard.add(btn)

        bot.send_message(
            message.chat.id,
            "✅ تم حفظ وصف الفكرة\n\n📂 اختر القسم المناسب للفكرة:",
            reply_markup=keyboard
        )

@bot.message_handler(content_types=['document'])
def handle_document(message):
    """معالج الملفات"""
    user_id = message.from_user.id
    user_state = get_user_state(user_id)

    if user_state == UserStates.WAITING_FOR_FILE:
        # ترجمة الملف
        translate_file(message)
    else:
        bot.send_message(
            message.chat.id,
            "📎 تم استلام الملف، لكن لست في وضع ترجمة الملفات حالياً.\n\nاذهب لقسم الترجمة أولاً."
        )

def translate_text(message, text):
    """ترجمة النص (محاكاة)"""
    # هنا يمكن إضافة API ترجمة حقيقي
    bot.send_message(
        message.chat.id,
        "🔄 جاري ترجمة النص..."
    )

    # محاكاة وقت المعالجة
    time.sleep(2)

    # ترجمة وهمية للعرض
    translated_text = f"📝 **النص الأصلي:**\n{text}\n\n🔄 **الترجمة:**\n[هنا ستظهر الترجمة الفعلية]\n\n✅ تمت الترجمة بنجاح!"

    bot.send_message(
        message.chat.id,
        translated_text,
        parse_mode='Markdown',
        reply_markup=create_main_keyboard()
    )

    set_user_state(message.from_user.id, UserStates.MAIN_MENU)

def translate_file(message):
    """ترجمة الملف (محاكاة)"""
    try:
        # الحصول على معلومات الملف
        file_name = message.document.file_name
        file_size = message.document.file_size

        # التحقق من حجم الملف
        if file_size > 20 * 1024 * 1024:  # 20 MB
            bot.send_message(
                message.chat.id,
                "❌ حجم الملف كبير جداً. الحد الأقصى 20 ميجابايت."
            )
            return

        bot.send_message(
            message.chat.id,
            f"📎 **تم استلام الملف:** {file_name}\n📊 **الحجم:** {file_size/1024:.1f} KB\n\n🔄 جاري المعالجة والترجمة..."
        )

        # محاكاة وقت المعالجة
        time.sleep(3)

        bot.send_message(
            message.chat.id,
            f"✅ **تمت ترجمة الملف بنجاح!**\n\n📄 اسم الملف: {file_name}\n📊 حالة الترجمة: مكتملة\n\n[هنا سيتم إرسال الملف المترجم]",
            parse_mode='Markdown',
            reply_markup=create_main_keyboard()
        )

        set_user_state(message.from_user.id, UserStates.MAIN_MENU)

    except Exception as e:
        logger.error(f"خطأ في ترجمة الملف: {e}")
        bot.send_message(
            message.chat.id,
            "❌ حدث خطأ في معالجة الملف. يرجى المحاولة مرة أخرى."
        )

# معالجة الأفكار المقترحة
@bot.callback_query_handler(func=lambda call: call.data.startswith("category_"))
def handle_idea_category(call):
    """معالجة اختيار قسم الفكرة"""
    user_id = call.from_user.id
    category = call.data.replace("category_", "")

    user_data_dict = get_user_data(user_id)
    idea_data = user_data_dict.get('idea', {})

    if category == "قسم جديد":
        # طلب اسم القسم الجديد
        set_user_state(user_id, UserStates.WAITING_FOR_IDEA_CATEGORY)
        bot.edit_message_text(
            "📂 اكتب اسم القسم الجديد:",
            call.message.chat.id,
            call.message.message_id
        )
        return

    idea_data['category'] = category

    # حفظ الفكرة في Firebase
    idea_record = {
        'name': idea_data.get('name', ''),
        'author': idea_data.get('author', ''),
        'description': idea_data.get('description', ''),
        'category': category,
        'user_id': user_id
    }

    idea_id = firebase_manager.save_idea(idea_record)

    # إشعار المشرفين
    for admin_id in ADMIN_IDS:
        try:
            admin_text = f"""
💡 **فكرة جديدة مقترحة**

📝 **اسم الفكرة:** {idea_data['name']}
👤 **صاحب الفكرة:** {idea_data['author']}
📂 **القسم:** {category}
📅 **التاريخ:** {idea_record['date']}

📋 **الوصف:**
{idea_data['description']}

🆔 **معرف المستخدم:** {user_id}
"""
            bot.send_message(admin_id, admin_text, parse_mode='Markdown')
        except:
            pass

    # رسالة شكر للمستخدم
    success_text = f"""
✅ **تم إرسال فكرتك بنجاح!**

📝 **ملخص الفكرة:**
• الاسم: {idea_data['name']}
• صاحب الفكرة: {idea_data['author']}
• القسم: {category}
• رقم الفكرة: #{idea_record['id']}

🔔 **سيتم مراجعة فكرتك من قبل الفريق المختص**
📧 **وسيتم التواصل معك في حالة الموافقة**

شكراً لمساهمتك في تطوير المنصة! 🙏
"""

    bot.edit_message_text(
        success_text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown'
    )

    # إرسال القائمة الرئيسية
    bot.send_message(
        call.message.chat.id,
        "🏠 العودة للقائمة الرئيسية:",
        reply_markup=create_main_keyboard()
    )

    set_user_state(user_id, UserStates.MAIN_MENU)

    # تنظيف بيانات المستخدم
    if 'idea' in user_data_dict:
        del user_data_dict['idea']

# أوامر إضافية للمشرفين
@bot.message_handler(commands=['admin'])
def admin_panel(message):
    """لوحة تحكم المشرفين"""
    user_id = message.from_user.id
    if user_id not in ADMIN_IDS:
        bot.send_message(message.chat.id, "❌ غير مصرح لك بالوصول لهذا الأمر")
        return

    keyboard = types.InlineKeyboardMarkup(row_width=2)
    btn_stats = types.InlineKeyboardButton("📊 الإحصائيات", callback_data="admin_stats")
    btn_ideas = types.InlineKeyboardButton("💡 الأفكار المقترحة", callback_data="admin_ideas")
    btn_users = types.InlineKeyboardButton("👥 المستخدمين", callback_data="admin_users")
    btn_broadcast = types.InlineKeyboardButton("📢 رسالة جماعية", callback_data="admin_broadcast")

    keyboard.add(btn_stats, btn_ideas)
    keyboard.add(btn_users, btn_broadcast)

    admin_text = """
🔧 **لوحة تحكم المشرفين**

اختر العملية المطلوبة:
"""

    bot.send_message(
        message.chat.id,
        admin_text,
        parse_mode='Markdown',
        reply_markup=keyboard
    )

@bot.callback_query_handler(func=lambda call: call.data.startswith("admin_"))
def handle_admin_callbacks(call):
    """معالجة أوامر المشرفين"""
    user_id = call.from_user.id
    if user_id not in ADMIN_IDS:
        bot.answer_callback_query(call.id, "❌ غير مصرح لك")
        return

    data = call.data

    if data == "admin_stats":
        # عرض الإحصائيات من Firebase
        try:
            stats = firebase_manager.get_bot_statistics()
            stats_text = f"""
📊 **إحصائيات البوت**

👥 **المستخدمين:**
• العدد الكلي: {stats.get('total_users', 0)}
• النشطين حالياً: {len(user_states)}

💡 **الأفكار المقترحة:**
• العدد الكلي: {stats.get('total_ideas', 0)}

🎯 **الاختبارات:**
• العدد الكلي: {stats.get('total_tests', 0)}

👨‍💼 **المشرفين:**
• النشطين: {stats.get('active_admins', 0)}

📚 **المحتوى:**
• المراحل الدراسية: {len(educational_content)}
• المواد: {sum(len(stage) for stage in educational_content.values())}
• الأسئلة الوزارية: {sum(len(subject) for subject in ministerial_questions.values())}

🛠️ **الخدمات:**
• عدد الخدمات: {len(services_data)}

📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات: {e}")
            stats_text = "❌ خطأ في تحميل الإحصائيات"

        bot.edit_message_text(
            stats_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown'
        )

    elif data == "admin_ideas":
        # عرض الأفكار المقترحة
        if not suggested_ideas:
            ideas_text = "💡 لا توجد أفكار مقترحة حالياً"
        else:
            ideas_text = "💡 **الأفكار المقترحة:**\n\n"
            for idea in suggested_ideas[-5:]:  # آخر 5 أفكار
                ideas_text += f"""
🆔 **#{idea['id']}** - {idea['name']}
� {idea['author']} | 📂 {idea['category']}
📅 {idea['date']}
📝 {idea['description'][:100]}...
➖➖➖➖➖➖➖➖➖➖
"""

        bot.edit_message_text(
            ideas_text,
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown'
        )

# معالجات الأخطاء والرسائل غير المتوقعة
@bot.message_handler(func=lambda message: True)
def handle_unknown_messages(message):
    """معالج الرسائل غير المعروفة"""
    user_id = message.from_user.id
    user_state = get_user_state(user_id)

    if user_state == UserStates.MAIN_MENU:
        unknown_text = """
🤔 **لم أفهم طلبك**

يرجى استخدام الأزرار الموجودة أدناه للتنقل في البوت.

💡 **نصائح:**
• استخدم الأزرار بدلاً من كتابة النص
• للمساعدة اكتب /help
• للبدء من جديد اكتب /start
"""

        bot.send_message(
            message.chat.id,
            unknown_text,
            parse_mode='Markdown',
            reply_markup=create_main_keyboard()
        )
    else:
        bot.send_message(
            message.chat.id,
            "🔄 يرجى إكمال العملية الحالية أو العودة للقائمة الرئيسية",
            reply_markup=create_back_keyboard()
        )

# وظائف مساعدة إضافية
def send_welcome_sticker(chat_id):
    """إرسال ملصق ترحيبي"""
    try:
        # يمكن إضافة ملصق ترحيبي هنا
        pass
    except:
        pass

def log_error(error_msg, user_id=None):
    """تسجيل الأخطاء"""
    error_text = f"ERROR: {error_msg}"
    if user_id:
        error_text += f" | User: {user_id}"
    logger.error(error_text)

def is_admin(user_id):
    """التحقق من صلاحيات المشرف"""
    return user_id in ADMIN_IDS

def format_number(number):
    """تنسيق الأرقام"""
    return f"{number:,}"

def get_user_info(user):
    """الحصول على معلومات المستخدم"""
    return {
        'id': user.id,
        'username': user.username or 'Unknown',
        'first_name': user.first_name or '',
        'last_name': user.last_name or '',
        'language_code': user.language_code or 'ar'
    }

# إعداد معالج الأخطاء العام
def error_handler(func):
    """مُزخرف لمعالجة الأخطاء"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"خطأ في {func.__name__}: {e}")
            if args and hasattr(args[0], 'chat'):
                try:
                    bot.send_message(
                        args[0].chat.id,
                        "❌ حدث خطأ مؤقت. يرجى المحاولة مرة أخرى."
                    )
                except:
                    pass
    return wrapper

# تطبيق معالج الأخطاء على الوظائف الرئيسية
start_command = error_handler(start_command)
handle_callback_query = error_handler(handle_callback_query)
handle_text_messages = error_handler(handle_text_messages)

if __name__ == "__main__":
    logger.info("�🚀 بدء تشغيل البوت التعليمي...")
    logger.info(f"📊 تم تحميل {len(educational_content)} مراحل دراسية")
    logger.info(f"🗂️ تم تحميل {len(ministerial_questions)} مواد وزارية")
    logger.info(f"🛠️ تم تحميل {len(services_data)} خدمات")

    try:
        # إرسال رسالة للمشرفين عند بدء التشغيل
        for admin_id in ADMIN_IDS:
            try:
                bot.send_message(
                    admin_id,
                    "🚀 **تم بدء تشغيل البوت بنجاح!**\n\n📅 " + datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                )
            except:
                pass

        logger.info("✅ البوت جاهز لاستقبال الرسائل")
        bot.polling(none_stop=True, interval=0, timeout=20)

    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        # إرسال إشعار للمشرفين عن الخطأ
        for admin_id in ADMIN_IDS:
            try:
                bot.send_message(
                    admin_id,
                    f"❌ **خطأ في البوت:**\n\n`{str(e)}`",
                    parse_mode='Markdown'
                )
            except:
                pass
