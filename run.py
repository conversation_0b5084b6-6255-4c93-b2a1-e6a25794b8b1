#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل البوت التعليمي
Educational Bot Runner

هذا الملف يقوم بتشغيل البوت مع معالجة الأخطاء والإعدادات المتقدمة
"""

import os
import sys
import logging
from educational_bot import *

def setup_environment():
    """إعداد البيئة والمتغيرات"""
    # التحقق من وجود ملف .env
    if os.path.exists('.env'):
        from dotenv import load_dotenv
        load_dotenv()
        
        # تحديث الإعدادات من متغيرات البيئة
        global BOT_TOKEN, ADMIN_IDS
        BOT_TOKEN = os.getenv('BOT_TOKEN', BOT_TOKEN)
        
        admin_ids_str = os.getenv('ADMIN_IDS', '')
        if admin_ids_str:
            ADMIN_IDS = [int(id.strip()) for id in admin_ids_str.split(',') if id.strip().isdigit()]

def check_requirements():
    """التحقق من المتطلبات"""
    if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
        print("❌ خطأ: يجب تعيين توكن البوت أولاً!")
        print("📝 قم بتعديل BOT_TOKEN في ملف educational_bot.py")
        print("🔗 احصل على التوكن من: https://t.me/BotFather")
        sys.exit(1)
    
    if not ADMIN_IDS or ADMIN_IDS == [123456789]:
        print("⚠️ تحذير: لم يتم تعيين معرفات المشرفين!")
        print("📝 قم بتعديل ADMIN_IDS في ملف educational_bot.py")

def main():
    """الدالة الرئيسية لتشغيل البوت"""
    print("🎓 بوت تيليجرام تعليمي متعدد الأقسام")
    print("=" * 50)
    
    # إعداد البيئة
    setup_environment()
    
    # التحقق من المتطلبات
    check_requirements()
    
    # عرض معلومات البوت
    print(f"🤖 توكن البوت: {BOT_TOKEN[:10]}...")
    print(f"👥 عدد المشرفين: {len(ADMIN_IDS)}")
    print(f"📚 المراحل الدراسية: {len(educational_content)}")
    print(f"🗂️ المواد الوزارية: {len(ministerial_questions)}")
    print(f"🛠️ الخدمات: {len(services_data)}")
    print("=" * 50)
    
    # تشغيل البوت
    try:
        print("🚀 بدء تشغيل البوت...")
        
        # إرسال رسالة للمشرفين عند بدء التشغيل
        for admin_id in ADMIN_IDS:
            try:
                bot.send_message(
                    admin_id,
                    "🚀 **تم بدء تشغيل البوت بنجاح!**\n\n📅 " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    parse_mode='Markdown'
                )
                print(f"✅ تم إرسال إشعار للمشرف: {admin_id}")
            except Exception as e:
                print(f"⚠️ فشل إرسال إشعار للمشرف {admin_id}: {e}")
        
        print("✅ البوت جاهز لاستقبال الرسائل")
        print("🔄 للإيقاف اضغط Ctrl+C")
        print("=" * 50)
        
        # بدء استقبال الرسائل
        bot.polling(none_stop=True, interval=0, timeout=20)
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
        
        # إرسال إشعار للمشرفين عند الإيقاف
        for admin_id in ADMIN_IDS:
            try:
                bot.send_message(
                    admin_id,
                    "🛑 **تم إيقاف البوت**\n\n📅 " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    parse_mode='Markdown'
                )
            except:
                pass
                
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        
        # إرسال إشعار للمشرفين عن الخطأ
        for admin_id in ADMIN_IDS:
            try:
                bot.send_message(
                    admin_id,
                    f"❌ **خطأ في البوت:**\n\n`{str(e)}`\n\n📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    parse_mode='Markdown'
                )
            except:
                pass
        
        sys.exit(1)

if __name__ == "__main__":
    main()
